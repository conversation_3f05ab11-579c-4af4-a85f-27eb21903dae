// 用户模型
model User {
  /// @comment 用户ID
  id                    String                @id @default(cuid()) @map("id")
  /// @comment 用户邮箱
  email                 String                @unique @map("email")
  /// @comment 邮箱验证状态
  emailVerified         Boolean?              @map("email_verified")
  /// @comment 用户状态
  status                String                @default("ACTIVE") @map("status")
  /// @comment 角色
  role                  String                @default("USER") @map("role")
  /// @comment 资源权限
  resourcePermissions   String[]              @map("resource_permissions") // 存储额外的资源权限ID列表
  /// @comment 用户名
  name                  String?               @map("name")
  /// @comment 用户头像
  avatar                String?               @map("avatar")
  /// @comment 用户密码
  password              String?               @map("password")
  /// @comment 用户信用点数
  credits               Int                   @default(0) @map("credits")
  /// @comment 用户GitHub ID
  githubId              String?               @map("github_id")
  /// @comment 用户GitHub Access Token
  githubAccessToken     String?               @map("github_access_token")
  /// @comment 用户GitHub Token
  githubToken           String?               @map("github_token")
  /// @comment 可用项目数
  availableProjectCount Int                   @default(0) @map("available_project_count")
  /// @comment 已用项目数
  usedProjectCount      Int                   @default(0) @map("used_project_count")
  /// @comment 用户优惠码
  promoCode             String?               @unique @map("promo_code") @db.VarChar(10)
  /// @comment 创建时间
  createdAt             DateTime              @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt             DateTime              @updatedAt @map("updated_at")
  /// @comment 网站
  websites              Website[]
  /// @comment 项目
  projects              Project[]
  /// @comment 项目使用记录
  projectCountHistories ProjectCountHistory[]
  /// @comment 用户推广
  userPromotions        UserPromotion[]
  /// @comment 用户游戏库
  userGameLibraries     UserGameLibrary[]
  /// @comment 游戏任务记录
  gameTaskRecords       GameTaskRecord[]
  /// @comment 提交任务
  submitTasks           SubmissionTask[]
  /// @comment 会话
  sessions              Session[]
  /// @comment 账号
  accounts              Account[]
  /// @comment 交易记录
  trades                Trade[]
  /// @comment 权益
  benefits              UserBenefit[]
  /// @comment 权益使用记录
  benefitUsages         UserBenefitUsage[]
  /// @comment 趋势用户关键词
  trendUserKeywords     TrendUserKeyword[]
  /// @comment 趋势关键词收藏
  trendFavorites        TrendFavorite[]
  /// @comment 项目游戏
  projectGames          ProjectGame[]
  /// @comment 后台任务
  backgroundTasks       BackgroundTask[]
  /// @comment 项目文章
  projectArticles       ProjectArticle[]
  /// @comment 提示词模板
  promptTemplates       PromptTemplate[]
  /// @comment 提示词测试记录
  promptTestRecords     PromptTestRecord[]

  @@map("t_user")
}

// 项目数量历史记录模型
model ProjectCountHistory {
  /// @comment 记录ID
  id        String   @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId    String   @map("user_id")
  /// @comment 操作类型 (increase/decrease)
  action    String   @map("action")
  /// @comment 变更数量
  amount    Int      @default(1) @map("amount")
  /// @comment 备注说明
  remark    String?  @map("remark")
  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 关联用户
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("t_project_count_history")
}

// 用户游戏库模型
model UserGameLibrary {
  /// @comment ID
  id              String  @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId          String  @map("user_id")
  /// @comment 游戏名称
  gameName        String  @map("game_name")
  /// @comment 游戏描述
  gameDescription String? @map("game_description") @db.Text
  /// @comment 游戏类型
  gameType        String  @default("iframe") @map("game_type")

  /// @comment 游戏iframe URL
  gameIframeUrl String? @map("game_iframe_url") @db.VarChar(500)
  /// @comment 游戏截图URL
  screenshotUrl String? @map("screenshot_url") @db.VarChar(500)

  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  /// @comment 游戏来源
  gameSource String @default("import") @map("game_source")

  // 关联
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("t_user_game_library")
}

// 用户推广模型
model UserPromotion {
  /// @comment 推广ID
  id         String   @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId     String   @map("user_id")
  /// @comment 服务商ID
  mediumId   String   @default("FaFaFa") @map("medium_id")
  /// @comment 站长ID
  campaignId String?  @map("campaign_id")
  /// @comment 密钥，接口调用需要
  secret     String?  @map("secret")
  /// @comment 创建时间
  createdAt  DateTime @default(now()) @map("created_at")
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("t_user_promotion")
}
