// 项目相关模型

// 项目模型
model Project {
  /// @comment 项目ID
  id                  String    @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId              String    @map("user_id")
  /// @comment 项目名称
  name                String    @map("name")
  /// @comment 模板代码
  templateCode        String    @map("template_code")
  /// @comment 模板版本
  templateVersion     String?   @map("template_version")
  /// @comment 模板类型
  templateType        String?   @map("template_type")
  /// @comment 模板URL
  templateUrl         String?   @map("template_url")
  /// @comment 部署主机类型
  hostType            String    @map("host_type")
  /// @comment 服务器ID（部署后固定）
  serverId            String?   @map("server_id")
  /// @comment Cloudflare账号ID（部署后固定）
  cloudflareAccountId String?   @map("cloudflare_account_id")
  /// @comment 域名
  domain              String?   @map("domain") @db.VarChar(100)
  /// @comment 子域名（部署后固定）
  subdomainName       String?   @map("subdomain_name")
  /// @comment 是否已部署
  isDeployed          Boolean   @default(false) @map("is_deployed")
  /// @comment 最后部署时间
  lastDeployedAt      DateTime? @map("last_deployed_at")
  /// @comment FTP用户名
  ftpUserName         String?   @map("ftp_user_name")
  /// @comment FTP密码
  ftpPassword         String?   @map("ftp_password")
  /// @comment FTP主目录
  ftpHomePath         String?   @map("ftp_home_path")
  /// @comment BT站点ID
  btSiteId            Int?      @map("bt_site_id")
  /// @comment 创建时间
  createdAt           DateTime  @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt           DateTime  @updatedAt @map("updated_at")

  user               User                       @relation(fields: [userId], references: [id], onDelete: Cascade)
  /// @comment 项目访问权限
  projectAccesses    ProjectAccess[]
  /// @comment 项目访问日志
  accessLogs         ProjectAccessLog[]
  /// @comment 游戏任务记录
  gameTaskRecords    GameTaskRecord[]
  /// @comment 部署记录
  deploymentRecords  ProjectDeploymentRecord[]
  /// @comment 服务器关联
  server             Server?                    @relation(fields: [serverId], references: [id], onDelete: SetNull)
  /// @comment Cloudflare账号关联
  cloudflareAccount  CloudflareAccount?         @relation(fields: [cloudflareAccountId], references: [id], onDelete: SetNull)
  /// @comment 域名绑定
  projectDomains     ProjectDomain[]
  /// @comment 网站设置模型
  siteSettings       ProjectSiteSetting[]
  // 国际化网站设置
  localeSiteSettings ProjectLocaleSiteSetting[]
  // 项目游戏
  games              ProjectGame[]
  // 国际化游戏设置
  gameLocales        ProjectGameLocale[]
  // 后台任务
  backgroundTasks    BackgroundTask[]
  // 提示词模板
  promptTemplates    PromptTemplate[]
  // 提示词测试记录
  promptTestRecords  PromptTestRecord[]
  // 提示词使用限制
  promptUsageLimits  ProjectPromptUsageLimit[]
  // 项目文章
  articles           ProjectArticle[]

  @@map("t_project")
}

// 项目网站设置
model ProjectSiteSetting {
  /// @comment 设置ID
  id                  String  @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId           String  @unique @map("project_id")
  /// @comment 默认语言
  defaultLocale       String  @map("locale") @db.VarChar(10)
  /// @comment 支持语言列表 [string]
  languanges          Json    @map("languanges")
  /// @comment 网站Logo
  logo                String? @map("logo")
  /// @comment 暗色Logo
  darkLogo            String? @map("dark_logo")
  /// @comment 联系邮箱
  contactEmail        String? @map("contact_email") @db.VarChar(100)
  /// @comment 图标和图片配置
  icons               Json?   @map("icons") // 包含 favicon, appleTouchIcon, androidIcon 等
  /// @comment 分析代码配置
  analytics           Json?   @map("analytics") // 包含 gaId, clarityId, plausible 等
  /// @comment 社交链接
  socialLinks         Json?   @map("social_links") // 包含 twitter, facebook, instagram 等
  /// @comment 字体配置 {sans:string,serif:string,mono:string}
  fonts               Json?   @map("fonts") // 包含 sans, serif, mono 等
  /// @comment 主题配置 {name:string,mainColor:string,secondaryColor:string}
  theme               Json?   @map("theme")
  /// @comment 自定义头部内容
  customHeaderContent String? @map("custom_header_content") @db.Text
  /// @comment ads.txt 内容
  adsTxtContent       String? @map("ads_txt_content") @db.Text

  /// @comment 友情链接
  friendLinks Json? @map("friend_links") // 包含 name, url 等

  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId])
  @@map("p_project_site_setting")
}

// 项目国际化网站设置
model ProjectLocaleSiteSetting {
  /// @comment 设置ID
  id        String   @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId String   @map("project_id")
  /// @comment 类型 metadata、nav、links、
  type      String
  /// @comment 语言
  locale    String
  /// @comment 内容
  content   Json?
  /// @comment 文本
  text      String?  @db.Text
  /// @comment 状态 PENDING、COMPLETED、FAILED
  status    String   @default("PENDING") @map("status")
  /// @comment 错误信息
  error     String?  @map("error")
  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, type, locale], name: "project_locale_site_setting_unique")
  @@index([projectId])
  @@index([status])
  @@map("p_project_locale_site_setting")
}

// 项目访问权限模型
model ProjectAccess {
  /// @comment 记录ID
  id         String             @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId  String             @map("project_id")
  /// @comment 被授权的用户邮箱
  userEmail  String             @map("user_email")
  /// @comment 创建时间
  createdAt  DateTime           @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt  DateTime           @updatedAt @map("updated_at")
  /// @comment 访问记录
  accessLogs ProjectAccessLog[]
  /// @comment 项目关联
  project    Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([projectId, userEmail])
  @@map("t_project_access")
}

// 项目访问日志模型
model ProjectAccessLog {
  /// @comment 记录ID
  id              String         @id @default(cuid()) @map("id")
  /// @comment 项目访问ID
  projectAccessId String?        @map("project_access_id")
  /// @comment 项目ID
  projectId       String         @map("project_id")
  /// @comment 访问用户邮箱
  userEmail       String         @map("user_email")
  /// @comment 访问IP
  ipAddress       String?        @map("ip_address")
  /// @comment 访问时间
  accessTime      DateTime       @default(now()) @map("access_time")
  /// @comment 项目权限关联
  projectAccess   ProjectAccess? @relation(fields: [projectAccessId], references: [id], onDelete: SetNull)
  /// @comment 项目关联
  project         Project        @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("t_project_access_log")
}

// 项目部署记录模型
model ProjectDeploymentRecord {
  /// @comment 部署记录ID
  id                  String    @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId           String    @map("project_id")
  /// @comment 服务器ID
  serverId            String?   @map("server_id")
  /// @comment Cloudflare账号ID
  cloudflareAccountId String?   @map("cloudflare_account_id")
  /// @comment 子域名
  subdomainName       String?   @map("subdomain_name")
  /// @comment 部署状态
  status              String    @default("PENDING") @map("status")
  /// @comment 部署类型 (PREVIEW/PRODUCTION)
  deploymentType      String    @default("PREVIEW") @map("deployment_type")
  /// @comment 部署消息
  message             String?   @map("message")
  /// @comment 错误信息
  error               String?   @map("error") @db.Text
  /// @comment 部署开始时间
  startTime           DateTime  @default(now()) @map("start_time")
  /// @comment 部署完成时间
  endTime             DateTime? @map("end_time")
  /// @comment 部署日志路径
  logFilePath         String?   @map("log_file_path")
  /// @comment 部署者ID
  deployedBy          String?   @map("deployed_by")
  /// @comment 消息ID
  messageId           String?   @map("message_id")
  /// @comment Jenkins构建编号
  buildNumber         Int?      @map("build_number")
  /// @comment Jenkins构建URL
  buildUrl            String?   @map("build_url")
  /// @comment Jenkins队列项编号
  queueItemNumber     Int?      @map("queue_item_number")
  /// @comment 创建时间
  createdAt           DateTime  @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt           DateTime  @updatedAt @map("updated_at")

  // 关联
  project           Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)
  server            Server?            @relation(fields: [serverId], references: [id], onDelete: SetNull)
  cloudflareAccount CloudflareAccount? @relation(fields: [cloudflareAccountId], references: [id], onDelete: SetNull)

  @@index([projectId])
  @@index([status])
  @@index([startTime])
  @@map("t_project_deployment_record")
}

// 项目域名绑定模型
model ProjectDomain {
  /// @comment 域名ID
  id          String   @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId   String   @map("project_id")
  /// @comment 域名
  domain      String   @map("domain")
  /// @comment 域名状态 (active/pending/error)
  status      String   @default("pending") @map("status")
  /// @comment 域名类型（temp/main）
  type        String   @default("MAIN") @map("type")
  zoneId      String?  @map("zone_id")
  nameServer1 String?  @map("name_server1")
  nameServer2 String?  @map("name_server2")
  /// @comment 创建时间
  createdAt   DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId])
  @@index([domain])
  @@map("t_project_domain")
}

// 服务器模型
model Server {
  /// @comment 服务器ID
  id         String @id @default(cuid()) @map("id")
  /// @comment 服务器名称
  name       String @map("name")
  /// @comment IP地址
  ipAddress  String @map("ip_address")
  /// @comment 宝塔面板URL
  btPanelUrl String @map("bt_panel_url")

  /// @comment 宝塔面板API密钥
  btPanelApiKey        String   @map("bt_panel_api_key")
  /// @comment SSH用户名
  sshUsername          String   @map("ssh_username")
  /// @comment SSH密码
  sshPassword          String   @map("ssh_password")
  /// @comment SSH私钥
  sshPrivateKey        String   @map("ssh_private_key")
  ///@comment 网站基础子域名
  baseDomain           String?  @map("base_domain")
  /// @comment 已部署网站数量
  deployedWebsiteCount Int      @default(0) @map("deployed_website_count")
  /// @comment 最大网站数量
  maxWebsiteCount      Int      @map("max_website_count")
  /// @comment 创建时间
  createdAt            DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt            DateTime @updatedAt @map("updated_at")

  // 关联
  projects          Project[]
  deploymentRecords ProjectDeploymentRecord[]

  @@map("t_server")
}

// Cloudflare账号模型
model CloudflareAccount {
  /// @comment Cloudflare账号ID
  id          String   @id @default(cuid()) @map("id")
  /// @comment 账号ID
  accountId   String   @map("account_id")
  /// @comment 邮箱
  email       String   @map("email")
  /// @comment 域名管理Token
  domainToken String?  @map("domain_token")
  /// @comment 当前域名数量
  domainCount Int      @default(0) @map("domain_count")
  /// @comment 创建时间
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联
  projects          Project[]
  deploymentRecords ProjectDeploymentRecord[]

  @@map("t_cloudflare_account")
}

// 项目文章模型
model ProjectArticle {
  /// @comment 文章ID
  id              String   @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId       String   @map("project_id")
  /// @comment 用户ID
  userId          String   @map("user_id")
  /// @comment 文章访问路径
  slug            String   @map("slug")
  /// @comment 文章标题
  title           String   @map("title")
  /// @comment 文章内容(MDX格式)
  mdxContent      String   @map("mdx_content") @db.Text
  /// @comment 语言
  locale          String   @map("locale") @db.VarChar(10)
  /// @comment 标题图片URL
  titleImageUrl   String?  @map("title_image_url")
  /// @comment 作者
  author          String?  @map("author")
  /// @comment 作者头像URL
  authorImageUrl  String?  @map("author_image_url")
  /// @comment 阅读时间
  readTime        String?  @map("read_time")
  /// @comment 标签列表
  tags            Json?    @map("tags") // string[]
  /// @comment 分类代码
  categoryCode    String?  @map("category_code")
  /// @comment 元数据信息
  metadata        Json     @map("metadata") // MetadataInfo
  /// @comment 文章状态 DRAFT|PUBLISHED|ARCHIVED
  status          String   @default("DRAFT") @map("status")
  /// @comment 创建时间
  createdAt       DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt       DateTime @updatedAt @map("updated_at")

  // 关联
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([projectId, slug, locale], name: "project_article_slug_locale_unique")
  @@index([projectId])
  @@index([userId])
  @@index([locale])
  @@index([status])
  @@index([categoryCode])
  @@map("p_project_article")
}
