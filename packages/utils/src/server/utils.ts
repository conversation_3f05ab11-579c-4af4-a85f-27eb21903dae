import sha256 from "crypto-js/sha256"
/**
 * Stole this from the @radix-ui/primitive
 * @see https://github.com/radix-ui/primitives/blob/main/packages/core/primitive/src/primitive.tsx
 */
export function composeEventHandlers<E>(
	originalEventHandler?: (event: E) => void,
	ourEventHandler?: (event: E) => void,
	{ checkForDefaultPrevented = true } = {},
) {
	return function handleEvent(event: E) {
		originalEventHandler?.(event)

		if (
			checkForDefaultPrevented === false ||
			!(event as unknown as Event).defaultPrevented
		) {
			return ourEventHandler?.(event)
		}
	}
}

export function formatNumber(num: number): string {
	return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}

export function formatDate(date: Date): string {
	return date.toLocaleDateString("zh-CN", {
		year: "numeric",
		month: "2-digit",
		day: "2-digit",
	})
}

/**
 * 从加密后的字符串中获取项目Id
 * @param str
 * @returns
 */
export function getProjectIdFromSHA256(str: string) {
	const [projectId, md5] = str.split("-")
	if (!projectId || !md5) {
		console.log(`Invalid input string: ${str}`)
		return null
	}
	// 万能密钥
	if (md5 === "iamboy") return projectId

	const expectedMd5 = generateProjectIdSHA256(projectId)
	if (str !== expectedMd5) {
		console.log(
			`MD5 mismatch for projectId: ${projectId},origin:${str},expected:${expectedMd5}`,
		)
		return null
	}

	return projectId
}
/**
 * 对项目Id进行加密，返回原始项目Id-加密后的项目Id
 * @param projectId
 * @returns
 */
export function generateProjectIdSHA256(projectId: string) {
	const secretKey =
		process.env.PROJECT_ID_SECRET_KEY || "qizhilu_com_secret_key"
	const hashDigest = sha256(projectId + secretKey)
	return `${projectId}-${hashDigest}`
}
