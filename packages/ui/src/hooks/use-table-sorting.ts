"use client"

import { useState, useCallback } from "react"
import { SortingState } from "@tanstack/react-table"

interface UseTableSortingOptions {
  /** 初始排序状态 */
  initialSorting?: SortingState
  /** 排序变化回调 */
  onSortingChange?: (sorting: SortingState) => void
}

interface UseTableSortingReturn {
  /** 排序状态 */
  sorting: SortingState
  /** 设置排序状态 */
  setSorting: (updater: SortingState | ((prev: SortingState) => SortingState)) => void
  /** 切换列排序 */
  toggleColumnSort: (columnId: string) => void
  /** 重置排序 */
  resetSorting: () => void
}

/**
 * 表格排序 Hook
 *
 * 用于管理表格排序状态
 *
 * @example
 * ```tsx
 * const {
 *   sorting,
 *   setSorting,
 *   toggleColumnSort,
 *   resetSorting,
 * } = useTableSorting({
 *   initialSorting: [{ id: 'name', desc: false }],
 *   onSortingChange: (sorting) => console.log('Sorting changed', sorting),
 * });
 * ```
 */
export function useTableSorting({
  initialSorting = [],
  onSortingChange,
}: UseTableSortingOptions = {}): UseTableSortingReturn {
  const [sorting, setSortingInternal] = useState<SortingState>(initialSorting)

  // 设置排序状态
  const setSorting = useCallback(
    (updater: any) => {
      // 处理函数形式和直接值形式
      const newSorting = typeof updater === 'function' ? updater(sorting) : updater
      setSortingInternal(newSorting)
      onSortingChange?.(newSorting)
    },
    [sorting, onSortingChange]
  )

  // 切换列排序
  const toggleColumnSort = useCallback(
    (columnId: string) => {
      setSorting((prevSorting: SortingState) => {
        // 查找当前列的排序状态
        const columnSortIndex = prevSorting.findIndex((sort: any) => sort.id === columnId)

        if (columnSortIndex === -1) {
          // 如果列未排序，添加升序排序
          return [...prevSorting, { id: columnId, desc: false }]
        } else {
          const currentSort = prevSorting[columnSortIndex]

          if (currentSort && !currentSort.desc) {
            // 如果当前是升序，切换为降序
            return prevSorting.map((sort: any, index: number) =>
              index === columnSortIndex ? { ...sort, desc: true } : sort
            )
          } else {
            // 如果当前是降序，移除排序
            return prevSorting.filter((_: any, index: number) => index !== columnSortIndex)
          }
        }
      })
    },
    [setSorting]
  )

  // 重置排序
  const resetSorting = useCallback(() => {
    setSorting([])
  }, [setSorting])

  return {
    sorting,
    setSorting,
    toggleColumnSort,
    resetSorting,
  }
}
