"use client"

import { useState, useMemo, useCallback } from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  VisibilityState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  RowSelectionState,
  Row,
  Table as TanstackTable,
} from "@tanstack/react-table"
import { useTablePagination } from "./use-table-pagination"
import { useTableSorting } from "./use-table-sorting"

interface UseTableOptions<TData, TValue> {
  /** 列定义 */
  columns: ColumnDef<TData, TValue>[]
  /** 表格数据 */
  data: TData[]
  /** 是否手动分页 */
  manualPagination?: boolean
  /** 总记录数 */
  totalItems?: number
  /** 初始页码 */
  initialPage?: number
  /** 初始每页条数 */
  initialPageSize?: number
  /** 页码变化回调 */
  onPageChange?: (page: number) => void
  /** 每页条数变化回调 */
  onPageSizeChange?: (pageSize: number) => void
  /** 初始列过滤器 */
  initialColumnFilters?: ColumnFiltersState
  /** 列过滤器变化回调 */
  onColumnFiltersChange?: (filters: ColumnFiltersState) => void
  /** 初始列可见性 */
  initialColumnVisibility?: VisibilityState
  /** 列可见性变化回调 */
  onColumnVisibilityChange?: (visibility: VisibilityState) => void
  /** 初始行选择 */
  initialRowSelection?: RowSelectionState
  /** 行选择变化回调 */
  onRowSelectionChange?: (selection: RowSelectionState) => void
  /** 是否加载中 */
  isLoading?: boolean
  /** 无数据时显示的消息 */
  noResultsMessage?: string
  /** 创建新项回调 */
  onCreate?: () => void
  /** 创建按钮文本 */
  onCreateLabel?: string
  /** 刷新数据回调 */
  onRefresh?: () => void
}

interface UseTableReturn<TData, TValue> {
  // 核心状态
  table: TanstackTable<TData>
  pagination: ReturnType<typeof useTablePagination>
  sorting: ReturnType<typeof useTableSorting>
  columnFilters: ColumnFiltersState
  setColumnFilters: (filters: ColumnFiltersState | ((prev: ColumnFiltersState) => ColumnFiltersState)) => void
  columnVisibility: VisibilityState
  setColumnVisibility: (visibility: VisibilityState | ((prev: VisibilityState) => VisibilityState)) => void
  rowSelection: RowSelectionState
  setRowSelection: (selection: RowSelectionState | ((prev: RowSelectionState) => RowSelectionState)) => void
  selectedRows: Row<TData>[]

  // 组件props
  tableProps: {
    table: TanstackTable<TData>
    columns: ColumnDef<TData, TValue>[]
    data: TData[]
    isLoading: boolean
  }
  filterProps: {
    table: TanstackTable<TData>
    onSubmit: (values: any) => void
    onReset: () => void
  }
  contentProps: {
    table: TanstackTable<TData>
    columns: ColumnDef<TData, TValue>[]
    isLoading: boolean
    noResultsMessage: string
  }
  paginationProps: {
    page: number
    totalPages: number
    onPageChange: (page: number) => void
    totalRecords?: number
  }
  toolbarProps: {
    table: TanstackTable<TData>
    isLoading: boolean
    onCreate?: () => void
    onCreateLabel?: string
    onRefresh?: () => void
  }
}

/**
 * 表格 Hook
 *
 * 用于管理表格状态和行为
 *
 * @example
 * ```tsx
 * const {
 *   table,
 *   pagination,
 *   sorting,
 *   tableProps,
 *   filterProps,
 *   contentProps,
 *   paginationProps
 * } = useTable({
 *   columns,
 *   data,
 *   manualPagination: true,
 *   totalItems: 100,
 *   initialPage: 1,
 *   initialPageSize: 10,
 *   onPageChange: (page) => fetchData(page),
 * });
 * ```
 */
export function useTable<TData, TValue>(options: UseTableOptions<TData, TValue>): UseTableReturn<TData, TValue> {
  const {
    columns,
    data,
    manualPagination = false,
    totalItems = 0,
    initialPage = 1,
    initialPageSize = 10,
    onPageChange,
    onPageSizeChange,
    initialColumnFilters = [],
    onColumnFiltersChange,
    initialColumnVisibility = {},
    onColumnVisibilityChange,
    initialRowSelection = {},
    onRowSelectionChange,
    isLoading = false,
    noResultsMessage = "没有找到数据",
    onCreate,
    onCreateLabel = "创建",
    onRefresh
  } = options;

  // 使用分页 Hook
  const pagination = useTablePagination({
    initialPage,
    initialPageSize,
    totalItems,
    onPageChange,
    onPageSizeChange,
  })

  // 使用排序 Hook
  const sorting = useTableSorting({})

  // 列过滤器状态
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(
    initialColumnFilters
  )

  // 列可见性状态
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    initialColumnVisibility
  )

  // 行选择状态
  const [rowSelection, setRowSelection] = useState<RowSelectionState>(
    initialRowSelection
  )

  // 处理列过滤器变化
  const handleColumnFiltersChange = useCallback(
    (updater: any) => {
      // 处理函数形式和直接值形式
      const newFilters = typeof updater === 'function' ? updater(columnFilters) : updater
      setColumnFilters(newFilters)
      onColumnFiltersChange?.(newFilters)
    },
    [columnFilters, onColumnFiltersChange]
  )

  // 处理列可见性变化
  const handleColumnVisibilityChange = useCallback(
    (updater: any) => {
      // 处理函数形式和直接值形式
      const newVisibility = typeof updater === 'function' ? updater(columnVisibility) : updater
      setColumnVisibility(newVisibility)
      onColumnVisibilityChange?.(newVisibility)
    },
    [columnVisibility, onColumnVisibilityChange]
  )

  // 处理行选择变化
  const handleRowSelectionChange = useCallback(
    (updater: any) => {
      // 处理函数形式和直接值形式
      const newSelection = typeof updater === 'function' ? updater(rowSelection) : updater
      setRowSelection(newSelection)
      onRowSelectionChange?.(newSelection)
    },
    [rowSelection, onRowSelectionChange]
  )

  // 创建表格实例
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: manualPagination ? undefined : getPaginationRowModel(),
    manualPagination,
    pageCount: pagination.totalPages,
    onSortingChange: (updater) => sorting.setSorting(updater),
    onColumnFiltersChange: handleColumnFiltersChange,
    onColumnVisibilityChange: handleColumnVisibilityChange,
    onRowSelectionChange: handleRowSelectionChange,
    state: {
      sorting: sorting.sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.pageSize,
      },
    },
  })

  // 计算选中的行
  const selectedRows = useMemo(() => {
    return table.getFilteredSelectedRowModel().rows
  }, [table])

  // 为各个组件准备props
  const tableProps = {
    table,
    columns,
    data,
    isLoading,
  }

  const filterProps = {
    table,
    onSubmit: (values: any) => {
      // 可以在这里处理过滤器提交
      console.log('Filter values:', values)
    },
    onReset: () => {
      // 可以在这里处理过滤器重置
      console.log('Filter reset')
    }
  }

  const contentProps = {
    table,
    columns,
    isLoading,
    noResultsMessage
  }

  const paginationProps = {
    page: pagination.page,
    totalPages: pagination.totalPages,
    onPageChange: pagination.setPage,
    totalRecords: totalItems
  }

  const toolbarProps = {
    table,
    isLoading,
    onCreate,
    onCreateLabel,
    onRefresh
  }

  return {
    // 核心状态
    table,
    pagination,
    sorting,
    columnFilters,
    setColumnFilters: handleColumnFiltersChange,
    columnVisibility,
    setColumnVisibility: handleColumnVisibilityChange,
    rowSelection,
    setRowSelection: handleRowSelectionChange,
    selectedRows,

    // 组件props
    tableProps,
    filterProps,
    contentProps,
    paginationProps,
    toolbarProps
  }
}
