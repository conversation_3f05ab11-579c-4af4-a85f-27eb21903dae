# 数据表格组件 (DataTable)

一套完整的数据表格组件系统，包括表格核心组件、过滤器、工具栏、分页等，以及配套的Hook，提供简洁易用的API，支持序号列和操作列的声明式定义。使用子组件模式构建表格，提供更灵活的定制能力。

## 组件特性

- 基于 TanStack Table v8 构建
- 支持排序、过滤、分页和行选择
- 提供序号列和操作列的声明式定义
- 内置工具栏和过滤器组件
- 响应式设计和主题支持
- 类型安全的 API
- 智能默认值和自动类型检测
- 使用子组件模式构建表格，提供更灵活的定制能力

## 基础用法

以下是使用子组件模式构建数据表格的示例：

### 简单表格（使用子组件模式）

```tsx
import {
  DataTable,
  DataTableContent,
  DataTablePagination,
  DataTableIndexColumn,
  DataTableActionsColumn,
  DataTableAction
} from "@repo/ui/components";
import { useTable } from "@repo/ui/hooks";
import { ColumnDef } from "@tanstack/react-table";
import { Edit, Eye } from "lucide-react";

// 定义数据类型
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  status: "active" | "inactive";
}

// 定义列 - 智能默认值会自动处理日期和状态格式化
const columns: ColumnDef<User, any>[] = [
  {
    accessorKey: "name",
    // header 会自动设置为 "Name"
  },
  {
    accessorKey: "email",
    // header 会自动设置为 "Email"
  },
  {
    accessorKey: "role",
    // header 会自动设置为 "Role"
  },
  {
    accessorKey: "createdAt",
    // 自动检测为日期类型并应用日期格式化
  },
  {
    accessorKey: "status",
    // 可以自定义格式化
    meta: {
      formatter: (row) => row.getValue("status") === "active" ? "活跃" : "禁用"
    }
  }
];

// 示例数据
const data: User[] = [
  {
    id: "1",
    name: "张三",
    email: "<EMAIL>",
    role: "管理员",
    createdAt: "2023-01-15T08:30:00Z",
    status: "active"
  },
  {
    id: "2",
    name: "李四",
    email: "<EMAIL>",
    role: "编辑",
    createdAt: "2023-02-20T14:15:00Z",
    status: "active"
  },
  {
    id: "3",
    name: "王五",
    email: "<EMAIL>",
    role: "用户",
    createdAt: "2023-03-10T09:45:00Z",
    status: "inactive"
  },
];

export default function UserTable() {
  // 使用 useTable hook 管理表格状态
  const {
    table,
    contentProps,
    paginationProps
  } = useTable({
    columns,
    data,
    manualPagination: false, // 使用自动分页模式
    initialPageSize: 10
  });

  return (
    <DataTable table={table} noResultsMessage="没有找到用户">
      <DataTableContent
        columns={columns}
        {...contentProps}
      >
        {/* 序号列 */}
        <DataTableIndexColumn
          header="序号"
          width={80}
          fixed={true}
        />

        {/* 操作列 */}
        <DataTableActionsColumn
          header="操作"
          width={200}
          fixed={true}
        >
          {(row) => (
            <div className="flex gap-2">
              <DataTableAction
                icon={<Eye size={16} />}
                label="查看"
                variant="ghost"
                onClick={() => console.log("查看", row.original.id)}
              />
              <DataTableAction
                icon={<Edit size={16} />}
                label="编辑"
                onClick={() => console.log("编辑", row.original.id)}
              />
            </div>
          )}
        </DataTableActionsColumn>
      </DataTableContent>

      <DataTablePagination {...paginationProps} />
    </DataTable>
  );
}
```

### 服务端分页模式

```tsx
import { useState, useEffect } from "react";
import {
  DataTable,
  DataTableContent,
  DataTablePagination,
  DataTableToolbar,
  DataTableFilter,
  DataTableFilterField,
  DataTableFilterActions,
  DataTableIndexColumn,
  DataTableActionsColumn,
  DataTableAction,

} from "@repo/ui/components";
import { useTable } from "@repo/ui/hooks";
import { Button, Input, Select } from "@repo/ui/components";
import { Edit, Trash, Eye, Plus, RefreshCw } from "lucide-react";
import { z } from "zod";

// 定义过滤条件schema
const filterSchema = z.object({
  search: z.string().optional(),
  status: z.enum(["active", "inactive", "all"]).optional()
});

export default function UserTableWithServerPagination() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalItems, setTotalItems] = useState(0);

  // 加载数据
  const loadData = async (page: number) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/users?page=${page}`);
      const result = await response.json();
      setData(result.data);
      setTotalItems(result.totalItems);
    } catch (error) {
      console.error("加载数据失败", error);
    } finally {
      setIsLoading(false);
    }
  };

  // 使用 useTable hook 管理表格状态
  const {
    table,
    tableProps,
    filterProps,
    contentProps,
    paginationProps,
    toolbarProps,
    pagination
  } = useTable({
    columns,
    data,
    manualPagination: true,
    totalItems,
    initialPage: 1,
    initialPageSize: 10,
    isLoading,
    onPageChange: (page) => {
      loadData(page);
    },
  });

  // 页码变化时重新加载数据
  useEffect(() => {
    loadData(pagination.page);
  }, [pagination.page]);

  // 创建新用户
  const handleCreate = () => {
    // 打开创建用户表单
    console.log("创建新用户");
  };

  // 刷新数据
  const handleRefresh = () => {
    loadData(pagination.page);
  };

  return (
    <DataTable table={table} isLoading={isLoading}>
      <DataTableFilter
        schema={filterSchema}
        {...filterProps}
      >
        <DataTableFilterField name="search">
          <Input placeholder="搜索用户..." />
        </DataTableFilterField>

        <DataTableFilterField name="status">
          <Select>
            <option value="all">所有状态</option>
            <option value="active">活跃</option>
            <option value="inactive">非活跃</option>
          </Select>
        </DataTableFilterField>

        <DataTableFilterActions>
          <Button type="submit">筛选</Button>
          <Button type="reset" variant="outline">重置</Button>
        </DataTableFilterActions>
      </DataTableFilter>

      <DataTableToolbar>
        <Button onClick={handleCreate}>
          <Plus className="mr-2 h-4 w-4" />
          创建用户
        </Button>
        <Button variant="outline" onClick={handleRefresh}>
          <RefreshCw className="mr-2 h-4 w-4" />
          刷新
        </Button>
      </DataTableToolbar>

      <DataTableContent
        columns={columns}
        {...contentProps}
      >
        {/* 序号列 */}
        <DataTableIndexColumn
          header="序号"
          width={80}
          fixed={true}
        />

        {/* 操作列 */}
        <DataTableActionsColumn
          header="操作"
          width={200}
          fixed={true}
        >
          {(row) => (
            <div className="flex gap-2">
              <DataTableAction
                icon={<Eye size={16} />}
                label="查看"
                variant="ghost"
                onClick={() => console.log("查看", row.original.id)}
              />
              <DataTableAction
                icon={<Edit size={16} />}
                label="编辑"
                onClick={() => console.log("编辑", row.original.id)}
              />
              <DataTableAction
                icon={<Trash size={16} />}
                label="删除"
                variant="destructive"
                onClick={() => console.log("删除", row.original.id)}
              />
            </div>
          )}
        </DataTableActionsColumn>
      </DataTableContent>

      <DataTablePagination {...paginationProps} />
    </DataTable>
  );
}
```

## 添加序号列和操作列

使用内置的序号列和操作列组件：

```tsx
import {
  DataTable,
  DataTableContent,
  DataTableIndexColumn,
  DataTableActionsColumn,
  DataTableAction
} from "@repo/ui/components";
import { useTable } from "@repo/ui/hooks";
import { Edit, Trash } from "lucide-react";

// 定义列
const columns = [
  // 普通列
  {
    accessorKey: "name",
    header: "姓名",
  },
  {
    accessorKey: "email",
    header: "邮箱",
  },
];

export default function UserTable() {
  const { table, contentProps } = useTable({
    columns,
    data,
  });

  return (
    <DataTable table={table}>
      <DataTableContent
        columns={columns}
        {...contentProps}
      >
        {/* 序号列 */}
        <DataTableIndexColumn
          header="序号"
          width={80}
          fixed={true}
        />

        {/* 操作列 */}
        <DataTableActionsColumn
          header="操作"
          width={200}
          fixed={true}
        >
          {(row) => (
            <div className="flex gap-2">
              <DataTableAction
                icon={<Edit className="w-4 h-4" />}
                onClick={() => handleEdit(row.original.id)}
              />
              <DataTableAction
                icon={<Trash className="w-4 h-4" />}
                variant="destructive"
                onClick={() => handleDelete(row.original.id)}
              />
            </div>
          )}
        </DataTableActionsColumn>
      </DataTableContent>
    </DataTable>
  );
}
```

## 服务端分页

服务端分页的完整示例已在前面的"服务端分页模式"部分展示。使用 useTable hook 可以轻松实现服务端分页：

```tsx
// 使用 useTable hook 管理表格状态
const {
  table,
  paginationProps,
  pagination
} = useTable({
  columns,
  data,
  manualPagination: true, // 启用手动分页
  totalItems: 100, // 总记录数
  initialPage: 1,
  initialPageSize: 10,
  onPageChange: (page) => {
    // 加载对应页的数据
    loadData(page);
  },
});

// 页码变化时重新加载数据
useEffect(() => {
  loadData(pagination.page);
}, [pagination.page]);

// 在 DataTable 中使用分页组件
<DataTable table={table}>
  {/* 其他组件... */}
  <DataTablePagination {...paginationProps} />
</DataTable>
```

## 使用 Hooks 进行高级定制

使用 `useTable` hook 进行更高级的定制：

```tsx
import { useState } from "react";
import { useTable } from "@repo/ui/hooks";
import {
  DataTable,
  DataTableFilter,
  DataTableToolbar,
  DataTableContent,
  DataTablePagination,
  DataTableIndexColumn,
  DataTableActionsColumn,
  DataTableAction
} from "@repo/ui/components";
import { Input, Select } from "@repo/ui/components";
import { Edit, Trash, Eye } from "lucide-react";
import { z } from "zod";

// 定义过滤条件schema
const filterSchema = z.object({
  search: z.string().optional(),
  status: z.enum(["active", "inactive", "all"]).optional()
});

export default function UsersTable() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // 使用 useTable hook
  const {
    table,
    tableProps,
    filterProps,
    contentProps,
    paginationProps
  } = useTable({
    columns,
    data,
    manualPagination: true,
    totalItems: 100,
    initialPage: 1,
    initialPageSize: 10,
    isLoading,
    onPageChange: (page) => {
      // 加载对应页的数据
      loadData(page);
    },
  });

  const columns = [
    { accessorKey: "name", header: "姓名" },
    { accessorKey: "email", header: "邮箱" },
    { accessorKey: "role", header: "角色" },
    { accessorKey: "status", header: "状态" }
  ];

  return (
    <DataTable {...tableProps}>
      <DataTableFilter
        schema={filterSchema}
        {...filterProps}
      >
        <DataTableFilter.Field name="search">
          <Input placeholder="搜索用户..." />
        </DataTableFilter.Field>

        <DataTableFilter.Field name="status">
          <Select>
            <option value="all">所有状态</option>
            <option value="active">活跃</option>
            <option value="inactive">非活跃</option>
          </Select>
        </DataTableFilter.Field>

        <DataTableFilter.Actions>
          <Button type="submit">筛选</Button>
          <Button type="reset" variant="outline">重置</Button>
        </DataTableFilter.Actions>
      </DataTableFilter>

      <DataTableToolbar>
        <Button>添加用户</Button>
        <Button>导出数据</Button>
      </DataTableToolbar>

      <DataTableContent
        columns={columns}
        {...contentProps}
      >
        {/* 序号列 */}
        <DataTableIndexColumn
          title="序号"
          width={80}
          fixed={true}
        />

        {/* 操作列 */}
        <DataTableActionsColumn
          title="操作"
          width={200}
          fixed={true}
        >
          {(row) => (
            <div className="flex gap-2">
              <DataTableAction
                icon={<Eye size={16} />}
                label="查看"
                variant="ghost"
                onClick={() => console.log("查看", row.id)}
              />
              <DataTableAction
                icon={<Edit size={16} />}
                label="编辑"
                onClick={() => console.log("编辑", row.id)}
              />
              <DataTableAction
                icon={<Trash size={16} />}
                label="删除"
                variant="destructive"
                onClick={() => console.log("删除", row.id)}
              />
            </div>
          )}
        </DataTableActionsColumn>
      </DataTableContent>

      <DataTablePagination {...paginationProps} />
    </DataTable>
  );
}
```

## 最佳实践

### 1. 列定义

- 使用 `accessorKey` 或 `accessorFn` 定义如何从数据中获取值
- 为每列提供唯一的 `id`（如果不提供，将自动使用 `accessorKey` 作为 `id`）
- 使用 `header` 定义列标题（如果不提供，将自动根据 `accessorKey` 生成）
- 使用 `cell` 自定义单元格渲染
- 使用 `formatter` 快速格式化列数据（日期和数字类型会自动检测并应用格式化）

```tsx
const columns = [
  {
    accessorKey: "name",
    // header 会自动设置为 "Name"
    cell: ({ row }) => <span className="font-medium">{row.getValue("name")}</span>,
  },
  {
    accessorKey: "createdAt",
    // 自动检测为日期类型并应用日期格式化
  },
  {
    accessorKey: "updatedAt",
    // 自动检测为日期时间类型并应用日期时间格式化
  },
  {
    accessorKey: "amount",
    // 自动检测为数字类型并应用数字格式化
  },
  {
    accessorKey: "status",
    // 可以自定义格式化
    meta: {
      formatter: (row) => row.getValue("status") === "active" ? "活跃" : "禁用"
    }
  },
];
```

### 2. 表格模式选择

- 使用 `mode="auto"` 进行客户端分页、排序和过滤（适用于小型数据集）
- 使用 `mode="server"` 进行服务端分页、排序和过滤（适用于大型数据集）
- 对于 `mode="auto"`，只需提供完整数据集和 `pageSize`
- 对于 `mode="server"`，需提供当前页数据、`page`、`totalPages` 和 `onPageChange` 回调

### 3. 状态管理

- 对于简单场景，使用 `DataTable` 组件
- 对于复杂场景，使用 `useTable` hook 管理状态
- 使用 `initialSorting`、`initialColumnFilters` 等属性设置初始状态
- 使用 `onSortingChange`、`onColumnFiltersChange` 等回调处理状态变化

### 4. 性能优化

- 使用 `useMemo` 缓存列定义和数据
- 避免在渲染循环中创建新函数
- 对于大数据集，使用 `mode="server"` 进行服务端分页
- 使用 `React.memo` 包装自定义单元格组件

```tsx
// 缓存列定义
const columns = useMemo(() => [
  // 列定义...
], []);

// 缓存数据转换
const processedData = useMemo(() => {
  return data.map(item => ({
    ...item,
    formattedDate: formatDate(item.date),
  }));
}, [data]);
```

### 5. 可访问性

- 确保表格有适当的 ARIA 属性
- 提供清晰的列标题
- 确保键盘导航可用
- 使用足够的颜色对比度

## 格式化器

数据表格支持以下内置格式化器：

### 日期格式化器 (date)

将日期值格式化为 YYYY-MM-DD 格式。

```tsx
{
  accessorKey: "createdAt",
  header: "创建时间",
  meta: {
    formatter: "date"
  }
}
```

### 日期时间格式化器 (datetime)

将日期时间值格式化为 YYYY-MM-DD HH:MM:SS 格式。

```tsx
{
  accessorKey: "updatedAt",
  header: "更新时间",
  meta: {
    formatter: "datetime"
  }
}
```

### 数字格式化器 (digit)

将数字值格式化为带千位分隔符的格式。

```tsx
{
  accessorKey: "amount",
  header: "金额",
  meta: {
    formatter: "digit"
  }
}
```

### 自定义格式化器

您可以提供自定义函数作为格式化器，该函数接收行对象并返回格式化后的字符串。

```tsx
{
  accessorKey: "status",
  header: "状态",
  meta: {
    formatter: (row) => {
      const status = row.getValue("status");
      return status === "active" ? "活跃" : "禁用";
    }
  }
}
```

## 组件 API

### DataTable

主表格容器组件，使用子组件模式构建表格。

```tsx
interface DataTableProps<TData, TValue> {
  /** 外部表格实例 */
  table?: Table<TData>
  /** 加载状态 */
  isLoading?: boolean
  /** 无数据时显示的消息 */
  noResultsMessage?: string
  /** 子组件 */
  children?: ReactNode
  /** 自定义类名 */
  className?: string

  // 以下属性仅用于内部表格实例创建，当提供 table 时不需要
  /** 列定义 */
  columns?: ColumnDef<TData, TValue>[]
  /** 表格数据 */
  data?: TData[]
  /** 表格模式 */
  mode?: TableMode
  /** 当前页码 */
  page?: number
  /** 总页数 */
  totalPages?: number
  /** 每页条数 */
  pageSize?: number
  /** 初始排序状态 */
  initialSorting?: SortingState
  /** 初始列过滤器状态 */
  initialColumnFilters?: ColumnFiltersState
  /** 初始列可见性状态 */
  initialColumnVisibility?: VisibilityState
  /** 初始行选择状态 */
  initialRowSelection?: RowSelectionState
  /** 排序变化回调 */
  onSortingChange?: (sorting: SortingState) => void
  /** 列过滤器变化回调 */
  onColumnFiltersChange?: (filters: ColumnFiltersState) => void
  /** 列可见性变化回调 */
  onColumnVisibilityChange?: (visibility: VisibilityState) => void
  /** 行选择变化回调 */
  onRowSelectionChange?: (selection: RowSelectionState) => void
}
```

### DataTableIndexColumn

序号列组件，用于在表格中添加序号列。

```tsx
interface DataTableIndexColumnProps<TData> {
  /** 自定义序号生成函数 */
  getIndex?: (row: Row<TData>) => number;
  /** 序号列标题 */
  header?: string;
  /** 列宽度 */
  width?: number;
  /** 是否固定列 */
  fixed?: boolean;
}
```

### DataTableActionsColumn

操作列组件，用于在表格中添加操作按钮列。

```tsx
interface DataTableActionsColumnProps<TData> {
  /** 操作列标题 */
  header?: string;
  /** 操作列宽度 */
  width?: number;
  /** 是否固定列 */
  fixed?: boolean;
  /** 渲染操作按钮的函数 */
  renderActions: (row: Row<TData>) => ReactNode;
}
```

### DataTableAction

表格操作按钮组件，用于在操作列中显示操作按钮。

```tsx
interface DataTableActionProps {
  /** 按钮文本 */
  label?: string;
  /** 按钮图标 */
  icon?: ReactNode;
  /** 点击回调 */
  onClick: () => void;
  /** 按钮变体 */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  /** 按钮大小 */
  size?: "default" | "sm" | "lg" | "icon";
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否加载中 */
  loading?: boolean;
}
```

### DataTableFilter

表格过滤器组件，用于在表格上方显示过滤条件。

```tsx
interface DataTableFilterProps<TData> {
  /** 表格实例 */
  table: Table<TData>
  /** 过滤器验证模式 */
  schema?: any
  /** 过滤器提交回调 */
  onSubmit?: (values: any) => void
  /** 过滤器重置回调 */
  onReset?: () => void
  /** 子组件 */
  children: ReactNode
}
```

#### DataTableFilter.Field

过滤器字段组件，用于在过滤器中添加字段。

```tsx
interface DataTableFilterFieldProps {
  /** 字段名称 */
  name: string
  /** 子组件 */
  children: ReactNode
  /** 自定义类名 */
  className?: string
}
```

#### DataTableFilter.Actions

过滤器操作组件，用于在过滤器中添加操作按钮。

```tsx
interface DataTableFilterActionsProps {
  /** 子组件 */
  children?: ReactNode
  /** 自定义类名 */
  className?: string
}
```

## Hooks API

### useTable

表格状态管理 Hook，用于管理表格的所有状态，并返回各个组件所需的 props。

```tsx
interface UseTableOptions<TData, TValue> {
  /** 列定义 */
  columns: ColumnDef<TData, TValue>[];
  /** 表格数据 */
  data: TData[];
  /** 是否手动分页 */
  manualPagination?: boolean;
  /** 总记录数 */
  totalItems?: number;
  /** 初始页码 */
  initialPage?: number;
  /** 初始每页条数 */
  initialPageSize?: number;
  /** 页码变化回调 */
  onPageChange?: (page: number) => void;
  /** 每页条数变化回调 */
  onPageSizeChange?: (pageSize: number) => void;
  /** 初始列过滤器 */
  initialColumnFilters?: ColumnFiltersState;
  /** 列过滤器变化回调 */
  onColumnFiltersChange?: (filters: ColumnFiltersState) => void;
  /** 初始列可见性 */
  initialColumnVisibility?: VisibilityState;
  /** 列可见性变化回调 */
  onColumnVisibilityChange?: (visibility: VisibilityState) => void;
  /** 初始行选择 */
  initialRowSelection?: RowSelectionState;
  /** 行选择变化回调 */
  onRowSelectionChange?: (selection: RowSelectionState) => void;
  /** 是否加载中 */
  isLoading?: boolean;
  /** 无数据时显示的消息 */
  noResultsMessage?: string;
  /** 创建新项回调 */
  onCreate?: () => void;
  /** 创建按钮文本 */
  onCreateLabel?: string;
  /** 刷新数据回调 */
  onRefresh?: () => void;
}

interface UseTableReturn<TData, TValue> {
  // 核心状态
  table: Table<TData>;
  pagination: ReturnType<typeof useTablePagination>;
  sorting: ReturnType<typeof useTableSorting>;
  columnFilters: ColumnFiltersState;
  setColumnFilters: (filters: ColumnFiltersState | ((prev: ColumnFiltersState) => ColumnFiltersState)) => void;
  columnVisibility: VisibilityState;
  setColumnVisibility: (visibility: VisibilityState | ((prev: VisibilityState) => VisibilityState)) => void;
  rowSelection: RowSelectionState;
  setRowSelection: (selection: RowSelectionState | ((prev: RowSelectionState) => RowSelectionState)) => void;
  selectedRows: Row<TData>[];

  // 组件props
  tableProps: {
    table: Table<TData>;
    columns: ColumnDef<TData, TValue>[];
    data: TData[];
    isLoading: boolean;
  };
  filterProps: {
    table: Table<TData>;
    onSubmit: (values: any) => void;
    onReset: () => void;
  };
  contentProps: {
    table: Table<TData>;
    columns: ColumnDef<TData, TValue>[];
    isLoading: boolean;
    noResultsMessage: string;
  };
  paginationProps: {
    page: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    totalRecords?: number;
  };
  toolbarProps: {
    table: Table<TData>;
    isLoading: boolean;
    onCreate?: () => void;
    onCreateLabel?: string;
    onRefresh?: () => void;
  };
}
```

### useTablePagination

表格分页 Hook，用于管理分页状态。

```tsx
interface UseTablePaginationOptions {
  /** 初始页码 */
  initialPage?: number;
  /** 初始每页条数 */
  initialPageSize?: number;
  /** 总记录数 */
  totalItems?: number;
  /** 页码变化回调 */
  onPageChange?: (page: number) => void;
  /** 每页条数变化回调 */
  onPageSizeChange?: (pageSize: number) => void;
}
```

### useTableSorting

表格排序 Hook，用于管理排序状态。

```tsx
interface UseTableSortingOptions {
  /** 初始排序状态 */
  initialSorting?: SortingState;
  /** 排序变化回调 */
  onSortingChange?: (sorting: SortingState) => void;
}
```
