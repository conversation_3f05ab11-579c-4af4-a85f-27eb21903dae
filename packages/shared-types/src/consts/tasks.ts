export enum SubmissionStatus {
  // 待处理
  Pending = 'PENDING',
  // 处理中
  Processing = 'PROCESSING',
  // 成功
  Success = 'SUCCESS',
  // 失败
  Failed = 'FAILED',
  // 完成
  Complete = 'COMPLETE',
}

export enum TaskStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

// 后台任务状态常量
export const BACKGROUND_TASK_STATUS = {
  // 待处理
  PENDING: 'PENDING',
  // 处理中
  PROCESSING: 'PROCESSING',
  // 处理成功
  SUCCESS: 'SUCCESS',
  // 失败进入队列
  FAILED_QUEUED: 'FAILED_QUEUED',
  // 处理失败
  FAILED: 'FAILED',
} as const

// 后台任务类型常量
export const BACKGROUND_TASK_TYPE = {
  // 翻译任务
  TRANSLATION: 'TRANSLATION',
} as const

// 后台任务状态类型
export type BackgroundTaskStatus = typeof BACKGROUND_TASK_STATUS[keyof typeof BACKGROUND_TASK_STATUS]

// 后台任务类型
export type BackgroundTaskType = typeof BACKGROUND_TASK_TYPE[keyof typeof BACKGROUND_TASK_TYPE]

export enum SocialPlatform {
  // YouTube平台
  Youtube = 'YOUTUBE',
  // Twitter平台
  Twitter = 'TWITTER',
  // TikTok平台
  Tiktok = 'TIKTOK',
  // Instagram平台
  Instagram = 'INSTAGRAM',
  // Facebook平台
  Facebook = 'FACEBOOK',
  // Reddit平台
  Reddit = 'REDDIT',
}
