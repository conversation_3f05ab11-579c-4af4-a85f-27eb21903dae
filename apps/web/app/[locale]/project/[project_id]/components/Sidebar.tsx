"use client"

import type { Project } from "@repo/db"
import { Link } from "@repo/i18n"
import {
	<PERSON>ton,
	Sidebar as ShadcnSidebar,
	SidebarContent,
	SidebarFooter,
	SidebarGroup,
	SidebarGroupContent,
	SidebarGroupLabel,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarProvider,
	SidebarSeparator,
} from "@repo/ui/components"
import {
	ArrowLeft,
	Cog,
	FileText,
	FolderTree,
	Gamepad2,
	ListOrdered,
	Megaphone,
	Globe,
	RefreshCw,
	Book,
	Clock,
} from "lucide-react"
import { useLocale } from "next-intl"
import { usePathname } from "next/navigation"

interface MenuItem {
	name: string
	href: string
	icon: React.ReactNode
	ownerOnly?: boolean // 只有项目所有者才能看到的菜单项
}

interface MenuGroup {
	title: string
	items: MenuItem[]
}

interface SidebarProps {
	project: Project
	isOwner?: boolean // 是否是项目所有者
}

export const Sidebar = ({ project, isOwner = true }: SidebarProps) => {
	const projectId = project.id
	const pathname = usePathname()

	const getLastPathSegment = (path: string) => {
		return path.split("/").pop() || ""
	}

	const isActive = (href: string) => {
		return getLastPathSegment(pathname) === getLastPathSegment(href)
	}

	// 菜单分组
	const menuGroups: MenuGroup[] = [
		{
			title: "内容管理",
			items: [
				{
					name: "游戏列表",
					href: `/project/${projectId}/games`,
					icon: <Gamepad2 className="w-4 h-4" />,
				},
				{
					name: "文章管理",
					href: `/project/${projectId}/articles`,
					icon: <FileText className="w-4 h-4" />,
				},
			],
		},
		{
			title: "网站管理",
			items: [
				{
					name: "基础设置",
					href: `/project/${projectId}/site-settings`,
					icon: <Cog className="w-4 h-4" />,
				},
				{
					name: "菜单设置",
					href: `/project/${projectId}/navigation`,
					icon: <ListOrdered className="w-4 h-4" />,
				},
				{
					name: "分类管理",
					href: `/project/${projectId}/categories`,
					icon: <FolderTree className="w-4 h-4" />,
				},
				{
					name: "广告设置",
					href: `/project/${projectId}/ad-settings`,
					icon: <Megaphone className="w-4 h-4" />,
				},
				{
					name: "提示词管理",
					href: `/project/${projectId}/prompts`,
					icon: <Book className="w-4 h-4" />,
				},
				
			],
		},
		
		{
			title: "部署与任务",
			items: [
				{
					name: "部署管理",
					href: `/project/${projectId}/deployment`,
					icon: <RefreshCw className="w-4 h-4" />,
				},
				{
					name: "域名绑定",
					href: `/project/${projectId}/domain-binding`,
					icon: <Globe className="w-4 h-4" />,
				},
				{
					name: "后台任务",
					href: `/project/${projectId}/background-tasks`,
					icon: <Clock className="w-4 h-4" />,
				},
			],
		},
	]

	// 根据用户是否是项目所有者过滤菜单项
	const filteredMenuGroups = isOwner
		? menuGroups
		: menuGroups.map((group) => ({
				...group,
				items: group.items.filter((item) => !item.ownerOnly),
			}))

	return (
		<SidebarProvider defaultOpen={true} open={true}>
			{/* 移动端遮罩层 */}
			<div
				className="fixed inset-0 bg-black/50 z-30 md:hidden opacity-0 pointer-events-none transition-opacity duration-300"
				data-sidebar-overlay="true"
				onClick={() => {
					const sidebar = document.querySelector('[data-sidebar="sidebar"]')
					const overlay = document.querySelector(
						'[data-sidebar-overlay="true"]',
					)
					if (sidebar && overlay) {
						sidebar.classList.remove("translate-x-0", "bg-gray-900")
						sidebar.classList.add("-translate-x-full")
						overlay.classList.remove("opacity-100", "pointer-events-auto")
						overlay.classList.add("opacity-0", "pointer-events-none")
					}
				}}
			/>

			<ShadcnSidebar side="left" variant="sidebar" data-sidebar="sidebar">
				{/* 顶部添加游戏按钮 */}
				<SidebarHeader>
					<div className="flex items-center gap-2 p-4">
						<Gamepad2 className="w-6 h-6" />
						<span className="text-lg font-medium">{project.name}</span>
					</div>
				</SidebarHeader>
				<SidebarSeparator className="data-[orientation=horizontal]:w-[95%]" />
				<SidebarContent>
					{filteredMenuGroups.map((group, groupIndex) => (
						<SidebarGroup key={groupIndex}>
							<SidebarGroupLabel>{group.title}</SidebarGroupLabel>
							<SidebarGroupContent>
								<SidebarMenu>
									{group.items.map((item) => (
										<SidebarMenuItem key={item.name}>
											<SidebarMenuButton
												asChild
												className={
													isActive(item.href)
														? "bg-primary text-white"
														: "text-gray-400 hover:bg-gray-800 hover:text-white"
												}
											>
												<Link
													href={item.href}
													className="flex items-center gap-2 px-2 py-1.5 rounded-md transition-colors duration-200"
												>
													<span className="text-lg">{item.icon}</span>
													<span>{item.name}</span>
												</Link>
											</SidebarMenuButton>
										</SidebarMenuItem>
									))}
								</SidebarMenu>
							</SidebarGroupContent>
						</SidebarGroup>
					))}
				</SidebarContent>

				{/* 底部返回按钮 */}
				<SidebarSeparator className="data-[orientation=horizontal]:w-[95%]" />
				<SidebarFooter>
					<Button
						variant="secondary"
						size="lg"
						asChild
						className="w-full bg-gray-800 hover:bg-gray-700 text-white border-gray-700"
					>
						<Link
							href="/dashboard/website-builder"
							className="flex items-center gap-3 justify-center font-medium"
						>
							<ArrowLeft className="w-5 h-5" />
							<span className="truncate">返回我的站点</span>
						</Link>
					</Button>
				</SidebarFooter>
			</ShadcnSidebar>
		</SidebarProvider>
	)
}

export default Sidebar
