"use client"

import {
	closestCenter,
	DndContext,
	DragEndEvent,
	KeyboardSensor,
	PointerSensor,
	useSensor,
	useSensors,
} from "@dnd-kit/core"
import {
	arrayMove,
	SortableContext,
	sortableKeyboardCoordinates,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { Button, Skeleton, Input } from "@repo/ui/components"
import {
	GripVertical,
	Trash,
	Search,
	Edit,
	FileText,
} from "lucide-react"
import { useEffect, useState } from "react"
import { useTranslations } from "next-intl"
import { toast } from "sonner"
import DeleteDialog from "./DeleteDialog"
import { ArticlePost } from "@repo/shared-types"

interface ArticleListProps {
	articles: ArticlePost[]
	selectedArticleId?: string | null
	onSelectArticle: (articleId: string) => void
	onEditArticle: (articleId: string) => void
	onDeleteArticle: (articleId: string) => void
	onReorderArticles: (articles: ArticlePost[]) => void
	isLoading?: boolean
}

// 可排序的文章项组件
function SortableArticleItem({
	article,
	isSelected,
	onSelect,
}: {
	article: ArticlePost
	isSelected: boolean
	onSelect: () => void
}) {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({ id: article.id })

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.5 : 1,
		zIndex: isDragging ? 1 : 0,
	}

	return (
		<div
			ref={setNodeRef}
			style={style}
			className={`mb-2 w-full ${isDragging ? "relative z-10" : ""}`}
		>
			<div
				className={`flex items-center py-2 px-3 cursor-pointer rounded-md ${
					isSelected ? "bg-primary/10" : "hover:bg-muted/50"
				}`}
				onClick={onSelect}
			>
				<div className="flex items-center w-full">
					<div {...attributes} {...listeners} className="cursor-grab">
						<GripVertical className="text-muted-foreground mr-2 h-3.5 w-3.5" />
					</div>
					<FileText className="mr-2 h-4 w-4 text-muted-foreground" />
					<div className="flex flex-col">
						<span className="font-medium text-sm">{article.title}</span>
						<span className="text-xs text-muted-foreground">
							{new Date(article.updateTime).toLocaleDateString()}
						</span>
					</div>
				</div>
			</div>
		</div>
	)
}

export default function ArticleList({
	articles,
	selectedArticleId,
	onSelectArticle,
	onEditArticle,
	onDeleteArticle,
	onReorderArticles,
	isLoading = false,
}: ArticleListProps) {
	const t = useTranslations("Articles")
	const [searchQuery, setSearchQuery] = useState("")
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
	const [articleToDelete, setArticleToDelete] = useState<ArticlePost | null>(null)
	const [localArticles, setLocalArticles] = useState<ArticlePost[]>([])

	// 当外部文章变化时更新本地文章
	useEffect(() => {
		setLocalArticles(articles)
	}, [articles])

	// 传感器配置
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	)

	// 处理拖拽结束事件
	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event

		if (over && active.id !== over.id) {
			const activeArticle = localArticles.find(
				(article) => article.id === active.id,
			)
			const overArticle = localArticles.find(
				(article) => article.id === over.id,
			)

			if (activeArticle && overArticle) {
				const oldIndex = localArticles.findIndex(
					(article) => article.id === active.id,
				)
				const newIndex = localArticles.findIndex(
					(article) => article.id === over.id,
				)

				if (oldIndex !== -1 && newIndex !== -1) {
					const reorderedArticles = arrayMove(localArticles, oldIndex, newIndex)

					setLocalArticles(reorderedArticles)
					onReorderArticles(reorderedArticles)
				}
			}
		}
	}

	// 处理删除文章
	const handleDeleteArticle = (article: ArticlePost) => {
		setArticleToDelete(article)
		setIsDeleteDialogOpen(true)
	}

	// 确认删除文章
	const confirmDeleteArticle = async () => {
		if (!articleToDelete) return

		try {
			onDeleteArticle(articleToDelete.id)
			setIsDeleteDialogOpen(false)
			setArticleToDelete(null)
		} catch (error) {
			toast.error(t("deleteArticleFailed"))
		}
	}

	// 过滤文章
	const filteredArticles = localArticles.filter((article) =>
		article.title.toLowerCase().includes(searchQuery.toLowerCase()),
	)

	return (
		<div className="space-y-4">
			<div className="relative">
				<Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
				<Input
					placeholder={t("searchArticles")}
					className="pl-8"
					value={searchQuery}
					onChange={(e) => setSearchQuery(e.target.value)}
				/>
			</div>

			{isLoading ? (
				<div className="space-y-2">
					{[1, 2, 3].map((i) => (
						<Skeleton key={i} className="h-10 w-full" />
					))}
				</div>
			) : (
				<DndContext
					sensors={sensors}
					collisionDetection={closestCenter}
					onDragEnd={handleDragEnd}
				>
					<div className="space-y-2">
						{filteredArticles.length > 0 ? (
							<SortableContext
								items={filteredArticles.map((article) => article.id)}
								strategy={verticalListSortingStrategy}
							>
								{filteredArticles.map((article) => (
									<div key={article.id} className="relative">
										<SortableArticleItem
											article={article}
											isSelected={article.id === selectedArticleId}
											onSelect={() => onSelectArticle(article.id)}
										/>
										<div
											className="flex items-center space-x-1 absolute right-3 top-1/2 -translate-y-1/2"
											onClick={(e) => e.stopPropagation()}
										>
											<Button
												variant="ghost"
												size="icon"
												className="h-7 w-7"
												onClick={(e) => {
													e.stopPropagation()
													onEditArticle(article.id)
												}}
											>
												<Edit className="h-3.5 w-3.5 text-muted-foreground hover:text-primary" />
											</Button>
											<Button
												variant="ghost"
												size="icon"
												className="h-7 w-7"
												onClick={(e) => {
													e.stopPropagation()
													handleDeleteArticle(article)
												}}
											>
												<Trash className="h-3.5 w-3.5 text-muted-foreground hover:text-destructive" />
											</Button>
										</div>
									</div>
								))}
							</SortableContext>
						) : (
							<div className="text-center py-6 text-muted-foreground text-sm">
								{searchQuery ? t("noArticlesFound") : t("noArticles")}
							</div>
						)}
					</div>
				</DndContext>
			)}

			<DeleteDialog
				isOpen={isDeleteDialogOpen}
				onClose={() => setIsDeleteDialogOpen(false)}
				onConfirm={confirmDeleteArticle}
				title={t("deleteArticle")}
				description={t("deleteArticleConfirmation")}
			/>
		</div>
	)
}
