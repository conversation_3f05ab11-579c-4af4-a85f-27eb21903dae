"use client"

import TranslationSwitcher from "@/lib/components/translation/TranslationSwitcher"
import { useTranslations } from "next-intl"
import { useArticles } from "../hooks/useArticles"
import ArticleEditor from "./ArticleEditor"
import ArticleList from "./ArticleList"
import { Button } from "@repo/ui/components"
import { Plus } from "lucide-react"
import { useArticleCategories } from "../hooks/useArticleCategories"

interface ArticlesProps {
	projectId: string
}

export default function Articles({ projectId }: ArticlesProps) {
	const t = useTranslations("Articles")

	// 使用自定义Hook获取文章数据
	const {
		isLoading,
		articles,
		selectedArticle,
		setSelectedArticle,
		addArticle: handleAddArticle,
		updateArticle: handleUpdateArticle,
		deleteArticle: handleDeleteArticle,
		reorderArticles: handleReorderArticles,
	} = useArticles(projectId)

	// 使用自定义Hook获取分类数据
	const {
		categories,
	} = useArticleCategories(projectId)

	return (
		<div className="grid md:grid-cols-5 gap-5">
			{/* 左侧文章列表 */}
			<div className="md:col-span-2">
				<div className="bg-card rounded-lg shadow-sm p-4 mb-5 border min-h-[480px]">
					<div className="flex justify-between items-center mb-4">
						<h3 className="text-base font-medium">{t("articleList")}</h3>
						<Button
							size="sm"
							onClick={handleAddArticle}
							disabled={isLoading}
						>
							<Plus className="h-4 w-4 mr-1" />
							{t("addArticle")}
						</Button>
					</div>
					<ArticleList
						articles={articles}
						selectedArticleId={selectedArticle?.id}
						onSelectArticle={(articleId) => {
							const article = articles.find((a) => a.id === articleId)
							if (article) {
								setSelectedArticle(article)
							}
						}}
						onEditArticle={(articleId) => {
							const article = articles.find((a) => a.id === articleId)
							if (article) {
								setSelectedArticle(article)
							}
						}}
						onDeleteArticle={handleDeleteArticle}
						onReorderArticles={handleReorderArticles}
						isLoading={isLoading}
					/>
				</div>
			</div>

			{/* 右侧编辑区 */}
			<div className="md:col-span-3">
				{selectedArticle ? (
					<div className="bg-card rounded-lg shadow-sm p-4 mb-5 border">
						<div className="flex justify-between items-start mb-4">
							<h3 className="text-base font-medium text-foreground">
								{t("editArticle")}
							</h3>
							<TranslationSwitcher />
						</div>
						<ArticleEditor
							key={`${selectedArticle?.id}`}
							isLoading={isLoading}
							article={selectedArticle}
							onSave={handleUpdateArticle}
							projectId={projectId}
							categories={categories}
						/>
					</div>
				) : (
					<div className="bg-card rounded-lg shadow-sm p-4 mb-5 min-h-[480px] border">
						<p className="text-muted-foreground text-center py-6 text-sm">
							{t("selectArticleToEdit")}
						</p>
					</div>
				)}
			</div>
		</div>
	)
}
