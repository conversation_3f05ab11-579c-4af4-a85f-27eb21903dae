"use client"

import { useTranslations } from "next-intl"
import { usePathname, Link } from "@repo/i18n"
import {
	SidebarTrigger,
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from "@repo/ui/components"
import { UserAuthSection } from "@/lib/components"
import { camel } from "radash"
import React from "react"

export default function TopNavbar() {
	const t = useTranslations("Dashboard")
	const pathname = usePathname()

	// 定义面包屑项类型
	type BreadcrumbItem = {
		label: string;
		href: string;
		isLast: boolean;
		isDirectory: boolean;
	}

	// 面包屑生成
	const generateBreadcrumbs = (): BreadcrumbItem[] => {
		const paths = pathname.split("/").filter(Boolean)

		// 创建面包屑项目
		const breadcrumbs = paths.map((path, index) => {
			// 跳过第一个"dashboard"路径，因为我们会单独添加它
			if (index === 0 && path === "dashboard") {
				return null
			}

			const href = `/${paths.slice(0, index + 1).join("/")}`
			const isLast = index === paths.length - 1

			// 检查是否为目录项（没有href的菜单项）
			const isDirectory = path.includes("settings") || path === "trend-keyword"

			return {
				label: t(`${camel(path)}`),
				href,
				isLast,
				isDirectory: isDirectory && !isLast // 只有非最后一项的目录才标记为目录
			}
		}).filter(Boolean) as BreadcrumbItem[] // 过滤掉null值并断言类型

		// 添加首页
		breadcrumbs.unshift({
			label: t("dashboard"),
			href: "/dashboard",
			isLast: paths.length === 1 && paths[0] === "dashboard",
			isDirectory: false
		})

		return breadcrumbs
	}

	const breadcrumbs = generateBreadcrumbs()

	return (
		<header className="flex flex-col sticky top-0 z-40 select-none bg-sidebar backdrop-blur-md">
			{/* 顶部导航栏 */}
			<div className="h-16 flex items-center px-4 md:px-8">
				{/* 移动端汉堡按钮 */}
				<SidebarTrigger className="md:hidden mr-2 text-white hover:bg-white/10" />

				{/* 面包屑导航 - 左侧 */}
				<div className="flex-1">
					<Breadcrumb>
						<BreadcrumbList>
							{breadcrumbs.map((crumb, index) => (
								<React.Fragment key={index}>
									{index > 0 && <BreadcrumbSeparator />}
									<BreadcrumbItem>
										{crumb.isLast || crumb.isDirectory ? (
											<BreadcrumbPage className="text-white">{crumb.label}</BreadcrumbPage>
										) : (
											<BreadcrumbLink asChild>
												<Link href={crumb.href}>{crumb.label}</Link>
											</BreadcrumbLink>
										)}
									</BreadcrumbItem>
								</React.Fragment>
							))}
						</BreadcrumbList>
					</Breadcrumb>
				</div>

				{/* 用户区 - 右侧 */}
				<div className="flex items-center gap-4">
					<UserAuthSection />
				</div>
			</div>
		</header>
	)
}
