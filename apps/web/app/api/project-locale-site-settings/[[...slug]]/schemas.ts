import { z } from "zod"

// 通用查询参数验证schema
export const queryParamsSchema = z.object({
	projectId: z.string(),
	locale: z.string().optional(),
	type: z.string().optional(),
})

// 获取单个设置的查询参数验证schema
export const getSettingSchema = z.object({
	projectId: z.string(),
	locale: z.string(),
	type: z.string(),
})

// 创建或更新设置的请求体验证schema
export const upsertSettingSchema = z.object({
	projectId: z.string(),
	locale: z.string(),
	type: z.string(),
	content: z.any(),
	text: z.string().optional(),
	status: z.string().optional().default("COMPLETED"),
	createTranslationTask: z.boolean().optional().default(false),
})

// 翻译设置的请求体验证schema
export const translateSettingSchema = z.object({
	projectId: z.string(),
	sourceLocale: z.string(),
	targetLocales: z.array(z.string()),
	type: z.string(),
	fieldsToTranslate: z.array(z.string()).optional(),
})

// 菜单项验证schema
export const menuItemSchema = z.object({
	id: z.string(),
	name: z.string(),
	label: z.string(),
	icon: z.string().optional(),
	type: z.string().optional(),
	path: z.string(),
	target: z.string().optional(),
	parentId: z.string().nullable().optional(),
	order: z.number().optional(),
	children: z.array(z.any()).optional(),
})

// 菜单内容验证schema
export const menuSchema = z.object({
	items: z.array(menuItemSchema).optional(),
})

// 分类项验证schema
export const categorySchema = z.object({
	code: z.string(),
	name: z.string(),
	icon: z.string().optional(),
	sortOrder: z.number().optional(),
	slug: z.string().optional(),
	locale: z.string().optional(),
	count: z.number().optional(),
	metadata: z
		.object({
			title: z.string().optional(),
			description: z.string().optional(),
		})
		.optional(),
})

// 分类数组验证schema
export const categoriesSchema = z.array(categorySchema)
