import { z } from "zod"
import { R } from "@repo/utils/server"
import { router, queryParamsSchema } from "."
import { ArticlePost, MetadataInfo } from "@repo/shared-types"
import { prisma } from "@repo/db"

// 文章列表参数验证schema
const articlesSchema = z
	.object({
		locale: z.string(),
		page: z.string().optional(),
		pageSize: z.string().optional(),
		category: z.string().optional(),
		tag: z.string().optional(),
		search: z.string().optional(),
	})
	.merge(queryParamsSchema)

// 文章详情参数验证schema
const articleDetailSchema = z
	.object({
		locale: z.string(),
		articleId: z.string(),
	})
	.merge(queryParamsSchema)

// 获取文章列表
router.get("/articles", async (request) => {
	try {
		const projectId = request.projectId as string
		const validation = articlesSchema.safeParse(request.query)

		if (!validation.success) {
			return R.error("参数验证失败")
		}

		const {
			locale,
			page = "1",
			pageSize = "10",
			category,
			tag,
			search,
		} = validation.data

		const take = Math.min(Number(pageSize), 20)
		const skip = (Number(page) - 1) * take

		// 构建查询条件
		const where: any = {
			projectId: projectId,
			locale: locale,
			...(search && { title: { contains: search } }),
			...(category && { categoryCode: category }),
		}

		// 根据标签筛选
		if (tag) {
			where.tags = {
				array_contains: [tag],
			}
		}

		// 查询文章列表
		const articles = await prisma.projectArticle.findMany({
			where,
			take,
			skip,
			orderBy: { updatedAt: "desc" },
			select: {
				id: true,
				slug: true,
				title: true,
				locale: true,
				titleImageUrl: true,
				author: true,
				authorImageUrl: true,
				readTime: true,
				tags: true,
				categoryCode: true,
				mdxContent: true,
				metadata: true,
				updatedAt: true,
			},
		})

		// 转换数据格式，将数据库字段映射到 ArticlePost 格式
		const formattedArticles: ArticlePost[] = articles.map((article) => ({
			id: article.id,
			slug: article.slug,
			title: article.title,
			locale: article.locale,
			titleImageUrl: article.titleImageUrl || "",
			author: article.author || undefined,
			authorImageUrl: article.authorImageUrl || undefined,
			readTime: article.readTime || undefined,
			tags: (article.tags as string[]) || [],
			mdxContent: article.mdxContent,
			metadata: article.metadata as unknown as MetadataInfo,
			updateTime: article.updatedAt.toISOString(),
			// 暂时不查询分类信息，避免性能问题
			category: article.categoryCode
				? {
						id: article.categoryCode,
						name: article.categoryCode,
						slug: article.categoryCode,
						count: 0,
					}
				: undefined,
		}))

		return R.ok(formattedArticles)
	} catch (error) {
		console.error("获取文章列表失败:", error)
		return R.error("获取文章列表失败")
	}
})

// 获取文章详情
router.get("/article-detail", async (request) => {
	try {
		const projectId = request.projectId as string
		const validation = articleDetailSchema.safeParse(request.query)

		if (!validation.success) {
			return R.error("参数验证失败")
		}

		const { locale, articleId } = validation.data

		// 查询文章详情
		const article = await prisma.projectArticle.findFirst({
			where: {
				projectId: projectId,
				locale: locale,
				status: "PUBLISHED", // 只返回已发布的文章
				OR: [
					{ id: articleId },
					{ slug: articleId }, // 支持通过 slug 查询
				],
			},
		})

		if (!article) {
			return R.error("文章不存在")
		}

		// 转换数据格式，将数据库字段映射到 ArticlePost 格式
		const formattedArticle: ArticlePost = {
			id: article.id,
			slug: article.slug,
			title: article.title,
			locale: article.locale,
			titleImageUrl: article.titleImageUrl || "",
			author: article.author || undefined,
			authorImageUrl: article.authorImageUrl || undefined,
			readTime: article.readTime || undefined,
			tags: (article.tags as string[]) || [],
			mdxContent: article.mdxContent,
			metadata: article.metadata as unknown as MetadataInfo,
			updateTime: article.updatedAt.toISOString(),
			// 暂时不查询分类信息，避免性能问题
			category: article.categoryCode
				? {
						id: article.categoryCode,
						name: article.categoryCode,
						slug: article.categoryCode,
						count: 0,
					}
				: undefined,
		}

		return R.ok(formattedArticle)
	} catch (error) {
		console.error("获取文章详情失败:", error)
		return R.error("获取文章详情失败")
	}
})
