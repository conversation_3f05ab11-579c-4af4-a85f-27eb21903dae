{"extends": "@repo/typescript-config/nextjs.json", "compilerOptions": {"paths": {"@/*": ["./*"], "@lib/*": ["./lib/*"], "@components/*": ["./lib/components/*"], "@styles/*": ["./lib/styles/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "../../packages/ui/src/components/data-table.tsx", "../../packages/ui/src/hooks/use-confirm.tsx", "../../packages/ui/src/hooks/useIsTouchDevice.ts", "../../packages/utils/src/server/TranslationService.ts"], "exclude": ["node_modules"]}