"use server"

import { authUser } from "@repo/auth/server"
import { prisma } from "@repo/db"
import { z } from "zod"
import {
  translateObject,
  createTranslationService,
  TranslationError
} from "../../../../packages/utils/src/server/TranslationService"
import { chatModel, MODEL_DEEPSEEK_CHAT } from "@repo/utils/server"
import { getLogger } from "@repo/logger"

const logger = getLogger("CategoryTranslation")

// 分类数据结构验证
const CategorySchema = z.object({
  code: z.string(),
  name: z.string(),
  icon: z.string(),
  sortOrder: z.number(),
  slug: z.string(),
  locale: z.string(),
  count: z.number().optional(),
  metadata: z.object({
    title: z.string(),
    description: z.string()
  })
})

// 分类数组验证
const CategoriesSchema = z.array(CategorySchema)

/**
 * 翻译分类数据
 * @param projectId 项目ID
 * @param sourceLocale 源语言
 * @param targetLocales 目标语言数组
 * @param type 分类类型 (game 或 article)
 */
export async function translateCategories(
  projectId: string,
  sourceLocale: string,
  targetLocales: string[],
  type: "game" | "article" = "game"
) {
  try {
    // 验证用户身份
    const user = await authUser(true)
    if (!user) {
      throw new Error("用户未登录")
    }

    // 验证项目所有权
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: { userId: true }
    })

    if (!project) {
      throw new Error("项目不存在")
    }

    if (project.userId !== user.id) {
      throw new Error("您没有权限更新此项目")
    }

    // 设置分类类型
    const settingType = type === "game" ? "game_categories" : "article_categories"

    // 获取源语言分类数据
    const sourceCategorySetting = await prisma.projectLocaleSiteSetting.findFirst({
      where: {
        projectId,
        locale: sourceLocale,
        type: settingType,
      },
    })

    if (!sourceCategorySetting || !sourceCategorySetting.content) {
      throw new Error(`源语言(${sourceLocale})分类数据不存在`)
    }

    // 验证源数据格式
    const sourceCategories = CategoriesSchema.parse(sourceCategorySetting.content)

    // 创建翻译服务
    const translationService = createTranslationService(chatModel(MODEL_DEEPSEEK_CHAT), 60000)

    // 存储翻译结果和错误信息
    const results = {
      success: [] as string[],
      failed: [] as { locale: string, error: string }[]
    }

    // 并行处理所有目标语言的翻译
    await Promise.all(targetLocales.map(async (targetLocale) => {
      try {
        // 标记为处理中 - 使用upsert和唯一索引
        await prisma.projectLocaleSiteSetting.upsert({
          where: {
            project_locale_site_setting_unique: {
              projectId,
              locale: targetLocale,
              type: settingType,
            },
          },
          update: {
            status: "PENDING",
            updatedAt: new Date(),
          },
          create: {
            projectId,
            locale: targetLocale,
            type: settingType,
            content: sourceCategories,
            status: "PENDING",
          },
        })

        // 逐个翻译分类对象，而不是整个数组
        const translatedCategories = await Promise.all(
          sourceCategories.map(async (category) => {
            try {
            // 对单个分类对象进行翻译
              return await translateObject(
                category,
                CategorySchema, // 使用单个对象的schema
                {
                  sourceLanguage: sourceLocale,
                  targetLanguage: targetLocale,
                  translateKeys: false,
                  fieldsToTranslate: ["name", "slug", "metadata.title", "metadata.description"],
                  timeout: 60000,
                },
                translationService
              )
            } catch (error) {
              // 如果单个分类翻译失败，记录错误但返回原始分类
              logger.warn(`翻译分类 ${category.code} 到 ${targetLocale} 失败:`, error)
              return category
            }
          })
        )

        // 保存翻译后的分类数据 - 使用upsert和唯一索引
        await prisma.projectLocaleSiteSetting.upsert({
          where: {
            project_locale_site_setting_unique: {
              projectId,
              locale: targetLocale,
              type: settingType,
            },
          },
          update: {
            content: translatedCategories,
            status: "COMPLETED",
            error: null, // 清除之前可能存在的错误信息
            updatedAt: new Date(),
          },
          create: {
            projectId,
            locale: targetLocale,
            type: settingType,
            content: translatedCategories,
            status: "COMPLETED",
          },
        })

        results.success.push(targetLocale)
      } catch (error) {
        logger.error(`翻译分类到 ${targetLocale} 失败:`, error)

        // 更新失败状态 - 使用upsert和唯一索引
        await prisma.projectLocaleSiteSetting.upsert({
          where: {
            project_locale_site_setting_unique: {
              projectId,
              locale: targetLocale,
              type: settingType,
            },
          },
          update: {
            status: "FAILED",
            error: error instanceof Error ? error.message : String(error),
            updatedAt: new Date(),
          },
          create: {
            projectId,
            locale: targetLocale,
            type: settingType,
            content: sourceCategories, // 使用源语言内容作为默认值
            status: "FAILED",
            error: error instanceof Error ? error.message : String(error),
          },
        })

        results.failed.push({
          locale: targetLocale,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }))

    return {
      success: true,
      results
    }
  } catch (error) {
    logger.error("翻译分类失败:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}
