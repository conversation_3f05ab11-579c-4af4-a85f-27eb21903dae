// 创建项目
export const OT_CREATE_PROJECT = "create_project"
// 创建全托管版本项目
export const OT_CREATE_PROJECT_SELF_HOST = "create_self_host_project"
// 获取项目详情
export const OT_GET_PROJECT_INFO = "get_project_info"
// 更新项目详情
export const OT_UPDATE_PROJECT_INFO = "update_project_info"

// 获取广告单元设置
export const OT_GET_AD_UNIT_SETTINGS = 'get_ad_unit_settings';
// 更新广告单元设置
export const OT_UPDATE_AD_UNIT_SETTINGS = 'update_ad_unit_settings';
// 获取Logo设置
export const OT_GET_LOGO_SETTINGS = "get_logo_settings"
// 更新Logo设置
export const OT_UPDATE_LOGO_SETTINGS = "update_logo_settings"
// 生成Logo
export const OT_GENERATE_LOGO = "generate_logo"
// 生成Icon
export const OT_GENERATE_ICON = "generate_icon"
// 保存Logo
export const OT_SAVE_LOGO = "save_logo"
// 保存Icon
export const OT_SAVE_ICON = "save_icon"
// 获取i18n数据
export const OT_GET_I18N_DATA = "get_i18n_data"
// 生成文案信息
export const OT_GENERATE_TEXT = "generate_text"
// 翻译其他语言
export const OT_TRANSLATE_OTHER_LANGUAGE = "translate_other_language"
// 保存语言json
export const OT_SAVE_LANGUAGE_JSON = "save_language_json"
// 获取指定语言json
export const OT_GET_LANGUAGE_JSON = "get_language_json"
// 获取git状态
export const OT_GIT_STATUS = "git_status"
// 拉取代码
export const OT_GIT_PULL = "git_pull"
// 提交代码
export const OT_GIT_PUSH = "git_push"
// 还原代码
export const OT_GIT_RESTORE = "git_restore"
// 读取文件
export const OT_READ_FILE = "read_file"
// 保存文件
export const OT_SAVE_FILE = "save_file"
// 读取t目录下的文件列表
export const OT_READ_T_FILES = "read_t_files"
// 创建t目录下的文件
export const OT_CREATE_T_FILE = "create_t_file"
// 删除t目录下的文件
export const OT_DELETE_T_FILE = "delete_t_file"
// 保存游戏设置
export const OT_SAVE_GAME_SETTINGS = "save_game_settings"
// 翻译所有导航栏
export const OT_TRANSLATE_ALL_NAVBAR = "translate_all_navbar"
// 获取资源json
export const OT_GET_RESOURCE_JSON = "get_resource_json"
// 保存资源json
export const OT_SAVE_RESOURCE_JSON = "save_resource_json"
// 翻译所有faq
export const OT_TRANSLATE_ALL_FAQS = "translate_all_faqs"
// 获取站点配置
export const OT_GET_SITE_CONFIG = "get_site_config"
// 保存友情链接
export const OT_SAVE_FRIEND_LINKS = "save_friend_links"
// 获取模板更新日志
export const OT_GET_TEMPLATE_CHANGELOG = "get_template_changelog"
// 执行模板更新
export const OT_PERFORM_TEMPLATE_UPDATE = "perform_template_update"
// 获取导航栏配置
export const OT_GET_NAVBAR_PATHS = "get_navigation_paths"
// 保存导航栏配置
export const OT_SAVE_NAVBAR_PATHS = "save_navigation_paths"
// 创建游戏页面
export const OT_CREATE_INTERACTIVE_FILE = "create_interactive_file"
// 获取所有游戏列表
export const OT_GET_GAME_LIST = "get_game_list"
// 删除游戏页面
export const OT_DELETE_GAME_PAGE = "delete_game_page"
// 添加游戏页面
export const OT_CREATE_GAME_FILE = "create_game_file"
// 获取游戏页面配置
export const OT_GET_GAME_SETTINGS = "get_game_settings"

// 获取推荐配置
export const OT_GET_RECOMMENDATION_PATHS = "get_recommendation_paths"
// 保存推荐配置
export const OT_SAVE_RECOMMENDATION_PATHS = "save_recommendation_paths"
// 检测游戏链接
export const OT_DETECT_GAME_URL = "detect_game_url"
// 翻译自定义页面
export const OT_TRANSLATE_T_FILE = "translate_t_file"
// 创建游戏内容
export const OT_CREATE_GAME_CONTENT = "create_game_content"

// 生成游戏功能内容
export const OT_GENERATE_FEATURES_CONTENT = "generate_features_content"
// 翻译游戏功能内容
export const OT_TRANSLATE_FEATURES_CONTENT = "translate_features_content"
// 修复MDX文件
export const OT_FIX_MDX_FILE = "fix_mdx_file"
// 下载项目
export const OT_DOWNLOAD_WORKSPACE = "download_workspace"
// 上传项目
export const OT_UPLOAD_WORKSPACE = "upload_workspace"

// 获取游戏分类配置
export const OT_GET_GAME_CATEGORIES = "get_game_categories"
// 保存游戏分类配置
export const OT_SAVE_GAME_CATEGORIES = "save_game_categories"

//删除git仓库
export const OT_DELETE_GIT_REPOSITORY = "delete_git_repository"

// 批量更新游戏分类
export const OT_BATCH_UPDATE_GAME_CATEGORIES = "batch_update_game_categories"

// 批量上传游戏库
export const OT_BATCH_UPLOAD_GAME_LIBRARY = "batch_upload_game_library"

// 批量排队插入单个游戏
export const OT_BATCH_INSERT_GAME = "batch_insert_game"

// 获取部署状态
export const OT_GET_DEPLOYMENT_STATUS = "get_deployment_status"

// 翻译任务
export const OT_TRANSLATION_TASK = "translation_task"
