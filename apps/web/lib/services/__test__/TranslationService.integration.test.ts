import {describe, expect, it} from "vitest"
import {z} from "zod"
import {
  DEFAULT_PROMPTS,
  translateObject,
  translateText,
  translateTextToMultipleLanguages,
} from "../../../../../packages/utils/src/server/TranslationService"
import {merge} from "radash"

/**
 * 这个测试文件包含实际数据的集成测试
 * 注意：这些测试会调用真实的AI服务，可能会产生费用
 * 运行方式：vitest TranslationService.integration.test.ts
 */
describe("TranslationService 集成测试", () => {
  // 使用真实的模型配置
  // const translationConfig = createTranslationService(chatModel(MODEL_DOUBAO_PRO_32K), 30000);

  describe("文本翻译", () => {
    it("应该翻译游戏说明文本", async () => {
      const sourceText =
        "这个游戏的操作非常简单，只需点击屏幕即可跳跃。收集金币并避开障碍物，尽可能地获得高分。"

      const result = await translateText(sourceText, {
        sourceLanguage: "zh-CN",
        targetLanguage: "en-US",
      })

      console.log("翻译结果:", result.translatedText)
      expect(result.translatedText).toBeTruthy()
      expect(result.translatedText).not.toBe(sourceText)
      expect(result.translatedText.toLowerCase()).toContain("jump")
      expect(result.translatedText.toLowerCase()).toContain("coins")
    })

    it("应该使用自定义提示词", async () => {
      const sourceText = "游戏加载中...请稍候"
      const customPrompt = `Translate the following loading message for a mobile game from Chinese to English.
      Make it sound casual and friendly, as if speaking directly to the player:

      "${sourceText}"`

      const result = await translateText(sourceText, {
        sourceLanguage: "zh-CN",
        targetLanguage: "en-US",
        customPrompt,
      })

      console.log("自定义提示词翻译结果:", result.translatedText)
      expect(result.translatedText).toBeTruthy()
      expect(result.translatedText.toLowerCase()).toContain("loading")
    })
  })

  describe("对象翻译", () => {
    it("应该翻译游戏UI对象", async () => {
      // 游戏UI文本
      const gameUIText = {
        title: "星际探险",
        startButton: "开始游戏",
        settingsButton: "设置",
        helpButton: "帮助",
        exitButton: "退出",
        loadingText: "正在加载游戏资源，请稍候...",
      }

      // 定义UI文本结构
      const gameUISchema = z.object({
        title: z.string(),
        startButton: z.string(),
        settingsButton: z.string(),
        helpButton: z.string(),
        exitButton: z.string(),
        loadingText: z.string(),
      })

      const result = await translateObject(gameUIText, gameUISchema, {
        sourceLanguage: "zh-CN",
        targetLanguage: "en-US",
      })

      console.log("游戏UI翻译结果:", result)
      expect(result.title).toBeTruthy()
      expect(result.startButton).toBeTruthy()
      expect(result.title).not.toBe(gameUIText.title)
      expect(result.startButton.toLowerCase()).toContain("start")
    })

    it("应该只翻译指定字段", async () => {
      const article = {
        title: "游戏开发指南",
        content: "本文将介绍如何开发一个简单的2D游戏。",
        author: "张三",
        publishDate: "2023-10-15",
      }

      const articleSchema = z.object({
        title: z.string(),
        content: z.string(),
        author: z.string(),
        publishDate: z.string(),
      })

      const result = await translateObject(article, articleSchema, {
        sourceLanguage: "zh-CN",
        targetLanguage: "en-US",
        fieldsToTranslate: ["title", "content"], // 只翻译标题和内容
      })

      console.log("部分字段翻译结果:", result)
      expect(result.title).not.toBe(article.title)
      expect(result.content).not.toBe(article.content)
      expect(result.author).toBe(article.author) // 作者名应保持不变
      expect(result.publishDate).toBe(article.publishDate) // 日期应保持不变
    })
  })

  describe("多语言翻译", () => {
    it("应该将文本翻译成多种语言", async () => {
      const sourceText = "欢迎来到我们的游戏世界！"

      const result = await translateTextToMultipleLanguages(sourceText, {
        sourceLanguage: "zh-CN",
        targetLanguages: ["en-US", "fr-FR", "es-ES"],
      })

      console.log("多语言翻译结果:", result)
      expect(result["en-US"]?.translatedText).toBeTruthy()
      expect(result["fr-FR"]?.translatedText).toBeTruthy()
      expect(result["es-ES"]?.translatedText).toBeTruthy()

      // 验证翻译结果不同
      expect(result["en-US"]?.translatedText).not.toBe(
        result["fr-FR"]?.translatedText,
      )
      expect(result["en-US"]?.translatedText).not.toBe(
        result["es-ES"]?.translatedText,
      )
    })
  })

  describe("DEFAULT_PROMPTS", () => {
    it("应该生成有效的文本翻译提示词", () => {
      const text = "游戏加载中..."
      const prompt = DEFAULT_PROMPTS.text(text, "zh-CN", "en-US", true)

      expect(prompt).toContain(text)
      expect(prompt).toContain("game localization project")
      expect(prompt).toContain("Preserve all formatting")
    })

    it("应该生成有效的对象翻译提示词", () => {
      const obj = {key1: "值1", key2: "值2"}
      const prompt = DEFAULT_PROMPTS.object(obj, "zh-CN", "en-US", true, [
        "key1",
      ])

      expect(prompt).toContain(JSON.stringify(obj, null, 2))
      expect(prompt).toContain("Translate both keys and values")
      expect(prompt).toContain("Only translate the following fields: key1")
    })
  })

  it("多级对象取值测试", () => {
    const tree = [
      {
        id: "1",
        label: "一级目录",
        href: "/1",
        children: [
          {
            id: "1-1",
            label: "二级目录",
            href: "/1-1",
            children: [
              {
                id: "1-1-1",
                label: "三级目录",
                href: "/1-1-1",
              },
            ],
          },
        ],
      }
    ]

    const schema = z.object({
      id: z.string(),
      label: z.string(),

      children: z.array(
        z.object({
          id: z.string(),
          label: z.string(),
          children: z.array(
            z.object({
              id: z.string(),
              label: z.string(),
            }),
          ),
        }),
      ),
    })

    const values = tree.map((v) => schema.parse(v))
    console.log("多级解析：", JSON.stringify(values))
    const news = [
      {
        id: "1",
        label: "一级目录1",
        children: [
          {
            id: "1-1",
            label: "二级目录1-1",
            children: [
              {
                id: "1-1-1",
                label: "三级目录1-1-1",
              },
            ],
          },
        ],
      }
    ]

    const merges = merge(tree, news, (f) => f.id)
    console.log("合并信息 =>", JSON.stringify(merges))
  })

})
