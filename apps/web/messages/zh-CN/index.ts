import AdSettings from "./ad-settings.json"
import Auth from "./auth.json"
import Common from "./common.json"
import CustomPages from "./custom-pages.json"
import Dashboard from "./dashboard.json"
import Deployment from "./deployment.json"
import DomainBinding from "./domain-binding.json"
import GameBox from "./game-box.json"
import GameLibrary from "./game-library.json"
import GameList from "./game-list.json"
import Games from "./games.json"
import GameSettings from "./game-settings.json"
import GitStatus from "./git-status.json"
import Home from "./home.json"
import I18nSettings from "./i18n-settings.json"
import KeywordResearch from "./keyword-research.json"
import LogoSettings from "./logo-settings.json"
import MDXEditor from "./mdx-editor.json"
import Navbar from "./navbar.json"
import NewGame from "./new-game.json"
import Payment from "./payment.json"
import Pricing from "./pricing.json"
import Servers from "./servers.json"
import Settings from "./settings.json"
import Submission from "./submission.json"
import Template from "./template.json"
import TrendKeywordList from "./trend-keyword-list.json"
import TrendKeywords from "./trend-keywords.json"
import TrendTaskAdmin from "./trend-task-admin.json"
import Users from "./users.json"
import Workspace from "./workspace.json"
import Preview from "./preview.json"
import Articles from "./articles.json"
import Tags from "./tags.json"

export default {
  ...AdSettings,
  ...Payment,
  ...Pricing,
  ...Auth,
  ...Common,
  ...CustomPages,
  ...Dashboard,
  ...GameBox,
  ...GameSettings,
  ...GameList,
  ...Games,
  ...GitStatus,
  ...Home,
  ...I18nSettings,
  ...KeywordResearch,
  ...LogoSettings,
  ...MDXEditor,
  ...Navbar,
  ...NewGame,
  ...Settings,
  ...Submission,
  ...Template,
  ...Users,
  ...Workspace,
  ...GameLibrary,
  ...Servers,
  ...DomainBinding,
  ...Deployment,
  ...TrendKeywordList,
  ...TrendKeywords,
  ...TrendTaskAdmin,
  ...Preview,
  ...Articles,
  ...Tags,
}
