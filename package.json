{"name": "qizhilu-platform", "private": true, "type": "module", "version": "0.0.1", "scripts": {"build": "turbo build", "build:analyze": "cross-env ANALYZE=true turbo build --filter=@repo/web", "dev": "turbo dev", "lint": "biome check .", "lint:fix": "biome check . --write", "setup": "bun run scripts/setup.ts", "clean-locales": "bun run scripts/clear-messages.js", "translate": "bun run scripts/translate-locale.js", "test": "vitest", "start": "turbo start", "db:generate": "turbo db:generate --filter=@repo/db", "db:migrate": "turbo db:migrate --filter=@repo/db", "db:rest": "turbo db:rest --filter=@repo/db", "db:push": "turbo db:push --filter=@repo/db", "db:dasborad": "turbo db:dasborad --filter=@repo/db", "dev:web": "turbo run dev --filter=@repo/web", "build:web": "turbo run build --filter=@repo/web", "start:web": "turbo run start --filter=@repo/web", "dev:rpa": "turbo run dev --filter=@repo/rpa", "build:rpa": "turbo run build --filter=@repo/rpa", "start:rpa": "turbo run start --filter=@repo/rpa", "dev:scheduler": "turbo run dev --filter=@repo/scheduler", "build:scheduler": "turbo run build --filter=@repo/scheduler", "start:scheduler": "turbo run start --filter=@repo/scheduler"}, "overrides": {"@types/react": "npm:types-react@rc", "@types/react-dom": "npm:types-react-dom@rc"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.1.18", "workspaces": ["apps/*", "packages/*"], "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ant-design/charts": "^2.3.0", "@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@ariakit/react": "^0.4.17", "@aws-sdk/client-s3": "^3.812.0", "@aws-sdk/s3-request-presigner": "^3.812.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@faker-js/faker": "^9.8.0", "@fingerprintjs/fingerprintjs": "^4.6.2", "@heroui/react": "^2.8.0-beta.4", "@heroui/theme": "2.4.15", "@hookform/resolvers": "^5.0.1", "@koa/router": "^13.1.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@mdxeditor/editor": "^3.32.3", "@melloware/react-logviewer": "^6.2.0", "@next/mdx": "^15.3.2", "@next/third-parties": "^15.3.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.8", "@types/jenkins": "^1.0.2", "add": "^2.0.6", "ai": "^4.3.16", "amqplib": "^0.10.8", "antd": "^5.25.1", "better-auth": "^1.2.8", "bun": "^1.2.13", "bytes": "^3.1.2", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "cloudflare": "^4.2.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cron": "^4.3.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.6.0", "follow-redirects": "^1.15.9", "framer-motion": "^12.12.1", "google-trends-api-code": "^4.9.10", "gray-matter": "^4.0.3", "html-to-text": "^9.0.5", "html2canvas": "^1.4.1", "https-proxy-agent": "^7.0.6", "imap": "^0.8.19", "itty-router": "^5.0.18", "jenkins": "^1.1.0", "jose": "^6.0.11", "json-bigint": "^1.0.0", "koa": "^3.0.0", "koa-bodyparser": "^4.4.1", "lucide-react": "^0.511.0", "mailparser": "^3.7.2", "mitt": "^3.0.1", "next": "^15.3.2", "next-app-theme": "^0.1.11", "next-intl": "^4.1.0", "next-mdx-remote": "^5.0.0", "next-themes": "^0.4.6", "node-cron": "^4.0.5", "nodemailer": "^7.0.3", "path-browserify": "^1.0.1", "pdf-lib": "^1.17.1", "playwright": "^1.52.0", "prismjs": "^1.30.0", "qcloud-cos-sts": "^3.1.3", "qrcode.react": "^4.2.0", "radash": "^12.1.0", "raw-loader": "^4.0.2", "react": "19.1.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "9.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-lite-youtube-embed": "^2.5.1", "react-markdown": "^10.1.0", "react-player": "^2.16.0", "react-query": "^3.39.3", "react-resizable-panels": "^3.0.2", "react-social-login-buttons": "^4.1.1", "react-textarea-autosize": "^8.5.9", "react-toastify": "^11.0.5", "react-tweet": "^3.2.2", "react-use": "^17.6.0", "read-excel-file": "^5.8.8", "recharts": "^2.15.3", "rehype-remark": "^10.0.1", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-mdx-frontmatter": "^5.1.0", "remark-stringify": "^11.0.0", "slate": "^0.114.0", "slate-dom": "^0.114.0", "slate-history": "^0.113.1", "slate-hyperscript": "^0.100.0", "slate-react": "^0.114.2", "sonner": "^2.0.3", "source-map-support": "^0.5.21", "sugar-high": "^0.9.3", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tailwind-scrollbar": "^4.0.2", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "^4.1.7", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.0", "use-file-picker": "^2.1.2", "vaul": "^1.1.2", "wechatmp-kit": "^0.1.2", "wechatpay-nextjs-v3": "^1.0.1", "winston": "^3.17.0", "winston-loki": "^6.1.3", "zod": "^3.24.4"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@huxy/copy-file-webpack-plugin": "^1.1.5", "@next/bundle-analyzer": "^15.3.2", "@playwright/test": "^1.52.0", "@prisma/nextjs-monorepo-workaround-plugin": "^6.8.2", "@repo/auth": "*", "@repo/db": "*", "@repo/i18n": "*", "@repo/logger": "*", "@repo/payment": "*", "@repo/typescript-config": "*", "@repo/ui": "*", "@repo/utils": "*", "@rspack/cli": "^1.3.10", "@rspack/core": "^1.3.10", "@stagewise/toolbar-next": "^0.1.2", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@turbo/codemod": "^2.5.2", "@types/amqplib": "^0.10.7", "@types/bcryptjs": "^2.4.6", "@types/blueimp-md5": "^2.18.2", "@types/bytes": "^3.1.5", "@types/canvas-confetti": "^1.9.0", "@types/crypto-js": "^4.2.2", "@types/follow-redirects": "^1.14.4", "@types/html-to-text": "^9.0.4", "@types/imap": "^0.8.42", "@types/jsdom": "^21.1.7", "@types/koa": "^2.15.0", "@types/koa-bodyparser": "^4.3.12", "@types/koa__router": "^12.0.4", "@types/lodash-es": "^4.17.12", "@types/mailparser": "^3.4.6", "@types/mdx": "^2.0.13", "@types/next": "latest", "@types/node": "^22.15.19", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.2", "@types/react": "npm:types-react@rc", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "npm:types-react-dom@rc", "@types/readline-sync": "^1.4.8", "@types/turndown": "^5.0.5", "@vitejs/plugin-react": "^4.4.1", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.21", "brotli": "^1.3.3", "bun-types": "^1.2.13", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.1", "cssnano": "^7.0.7", "csv-stringify": "^6.5.2", "csvtojson": "^2.0.10", "dotenv-webpack": "^8.1.0", "fast-csv": "^5.0.2", "jsdom": "^26.1.0", "postcss": "^8.5.3", "prisma": "^6.8.2", "prisma-query-log": "^3.2.1", "turbo": "^2.5.3", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3", "uploadthing": "7.7.2", "vercel": "^41.7.8", "vitest": "^3.1.3"}}